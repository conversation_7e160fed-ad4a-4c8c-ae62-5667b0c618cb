package com.jky.anze.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @description:
 * @author: cq
 * @date: 2025/4/30 16:58
 */

@Data
public class DgPcProjectItemVo {

    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("projectId")
    private String projectId;

    @ApiModelProperty("所属项目")
    private String projectName;
    @ApiModelProperty("项目名称")
    private String consSiteName;

    @ApiModelProperty("责任人")
    private String liabler;

    @ApiModelProperty("服务报告状态")
    private Integer reportType = 0;


    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty("服务记录次数")
    private Integer serviceCount;

    @ApiModelProperty("保单上传状态")
    private Integer hasInsuranceOrder = 0;

    @ApiModelProperty("机构关联状态")
    private Integer hasOrg = 0;

    @ApiModelProperty("合同价格")
    private Double agreementPrice;

    @ApiModelProperty("建设规模")
    private Double consSize;

    @ApiModelProperty("合同金额偏低状态")
    private Integer isLowContractAmount;
}


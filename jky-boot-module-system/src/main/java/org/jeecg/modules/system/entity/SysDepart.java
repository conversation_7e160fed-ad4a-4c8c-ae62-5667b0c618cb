package org.jeecg.modules.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * <p>
 * 部门表
 * <p>
 *
 * <AUTHOR>
 * @Since  2019-01-22
 */
@Data
@TableName("sys_depart")
public class SysDepart implements Serializable {
    private static final long serialVersionUID = 1L;

	/**ID*/
	@TableId(type = IdType.ASSIGN_ID)
	private String id;
	/**父机构ID*/
	private String parentId;
	/**机构/部门名称*/
	@Excel(name="机构/部门名称",width=15)
	private String departName;
	/**英文名*/
	@Excel(name="英文名",width=15)
	private String departNameEn;
	/**缩写*/
	private String departNameAbbr;
	/**排序*/
	@Excel(name="排序",width=15)
	private Integer departOrder;
	/**描述*/
	@Excel(name="描述",width=15)
	private String description;
	/**机构类别 1公司，2组织机构，2岗位*/
	@Excel(name="机构类别",width=15,dicCode="org_category")
	private String orgCategory;
	/**机构类型*/
	private String orgType;
	/**机构编码*/
	@Excel(name="机构编码",width=15)
	private String orgCode;
	/**手机号*/
	@Excel(name="手机号",width=15)
	private String mobile;
	/**传真*/
	@Excel(name="传真",width=15)
	private String fax;
	/**地址*/
	@Excel(name="地址",width=15)
	private String address;
	/**备注*/
	@Excel(name="备注",width=15)
	private String memo;
	/**状态（1启用，0不启用）*/
	@Dict(dicCode = "depart_status")
	private String status;
	/**删除状态（0，正常，1已删除）*/
	@Dict(dicCode = "del_flag")
	private String delFlag;
	/**对接企业微信的ID*/
	private String qywxIdentifier;
	/**创建人*/
	private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date createTime;
	/**更新人*/
	private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

	//update-begin---author:wangshuai ---date:20200308  for：[JTC-119]在部门管理菜单下设置部门负责人，新增字段负责人ids和旧的负责人ids
    /**部门负责人的ids*/
	@TableField(exist = false)
	private String directorUserIds;
    /**旧的部门负责人的ids(用于比较删除和新增)*/
	@TableField(exist = false)
    private String oldDirectorUserIds;
    //update-end---author:wangshuai ---date:20200308  for：[JTC-119]新增字段负责人ids和旧的负责人ids

	/**
	 * 组织机构代码
	 */
	private String organizationId;

	/**
	 * 邮箱
	 */
	private String mail;

	/**
	 * 企业类型
	 */
	private String entType;

	/**
	 * 营业执照
	 */
	private String filePath;
	/**
	 * 所属镇街ID
	 *
	 */
	private String townShipId;

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;
		if (o == null || getClass() != o.getClass()) return false;
		SysDepart sysDepart = (SysDepart) o;
		return Objects.equals(id, sysDepart.id) && Objects.equals(parentId, sysDepart.parentId) && Objects.equals(departName, sysDepart.departName) && Objects.equals(departNameEn, sysDepart.departNameEn) && Objects.equals(departNameAbbr, sysDepart.departNameAbbr) && Objects.equals(departOrder, sysDepart.departOrder) && Objects.equals(description, sysDepart.description) && Objects.equals(orgCategory, sysDepart.orgCategory) && Objects.equals(orgType, sysDepart.orgType) && Objects.equals(orgCode, sysDepart.orgCode) && Objects.equals(mobile, sysDepart.mobile) && Objects.equals(fax, sysDepart.fax) && Objects.equals(address, sysDepart.address) && Objects.equals(memo, sysDepart.memo) && Objects.equals(status, sysDepart.status) && Objects.equals(delFlag, sysDepart.delFlag) && Objects.equals(qywxIdentifier, sysDepart.qywxIdentifier) && Objects.equals(createBy, sysDepart.createBy) && Objects.equals(createTime, sysDepart.createTime) && Objects.equals(updateBy, sysDepart.updateBy) && Objects.equals(updateTime, sysDepart.updateTime) && Objects.equals(directorUserIds, sysDepart.directorUserIds) && Objects.equals(oldDirectorUserIds, sysDepart.oldDirectorUserIds) && Objects.equals(organizationId, sysDepart.organizationId) && Objects.equals(mail, sysDepart.mail) && Objects.equals(entType, sysDepart.entType) && Objects.equals(townShipId, sysDepart.townShipId);
	}

	@Override
	public int hashCode() {
		return Objects.hash(id, parentId, departName, departNameEn, departNameAbbr, departOrder, description, orgCategory, orgType, orgCode, mobile, fax, address, memo, status, delFlag, qywxIdentifier, createBy, createTime, updateBy, updateTime, directorUserIds, oldDirectorUserIds, organizationId, mail, entType, townShipId);
	}
}

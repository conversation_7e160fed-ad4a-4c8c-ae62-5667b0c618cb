package com.jky.anze.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.anze.domain.DganzeInsuranceServiceRecord;
import com.jky.anze.domain.vo.InsuranceRecordListVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DganzeInsuranceServiceRecordMapper extends BaseMapper<DganzeInsuranceServiceRecord> {
    IPage<InsuranceRecordListVo> queryServiceTodoList(Page<Object> objectPage, @Param("townShipId") String townShipId, @Param("id") String id, @Param("projectId") List<String> projectId);
}

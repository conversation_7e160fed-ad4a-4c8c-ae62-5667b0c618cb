package com.jky.anze.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jky.anze.domain.bo.DgdocPcProjectBo;
import com.jky.anze.domain.bo.RecordResultBo;
import com.jky.anze.domain.vo.DgPcProjectItemVo;
import com.jky.anze.domain.vo.PcProjectDetailVo;
import org.jeecg.modules.online.config.exception.BusinessException;

import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: cq
 * @date: 2025/4/30 16:52
 */

public interface IPcProjectService {
    IPage<DgPcProjectItemVo> queryProjectItemVo(DgdocPcProjectBo bo, Integer pageNo, Integer pageSize) throws BusinessException;

    Map<Integer, List<PcProjectDetailVo>> eventRecordList(String id);

    Boolean uploadResult(RecordResultBo bo) throws BusinessException;
}

package com.jky.anze.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.anze.constant.EntType;
import com.jky.anze.constant.InsuranceTypeConstant;
import com.jky.anze.domain.AnzeInsuranceEventRecord;
import com.jky.anze.domain.AnzeSysDepart;
import com.jky.anze.domain.DganzeInsurance;
import com.jky.anze.domain.DganzeInsuranceOrgServiceRecordServiceResult;
import com.jky.anze.domain.DganzeInsuranceServiceRecord;
import com.jky.anze.domain.DganzeServiceRecordFile;
import com.jky.anze.domain.bo.DgOrgServiceQueryBo;
import com.jky.anze.domain.bo.DgdocInsuranceOrderBo;
import com.jky.anze.domain.vo.DganzeInsuranceServiceRecordVo;
import com.jky.anze.domain.vo.InsuranceOrderListVo;
import com.jky.anze.domain.vo.InsuranceRecordListVo;
import com.jky.anze.mapper.AnzeInsuranceEventRecordMapper;
import com.jky.anze.mapper.AnzeSysDepartMapper;
import com.jky.anze.mapper.DganzeInsuranceMapper;
import com.jky.anze.mapper.DganzeInsuranceOrgServiceRecordServiceResultMapper;
import com.jky.anze.mapper.DganzeInsuranceServiceRecordMapper;
import com.jky.anze.mapper.DganzeServiceRecordFileMapper;
import com.jky.anze.service.IInsuranceService;
import com.jky.anze.util.LoginUserHelper;
import com.jky.dgdoc.mapper.DgdocAqjdConsSiteMapper;
import com.jky.modules.estar.tw.util.StringUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.online.config.exception.BusinessException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: cq
 * @date: 2025/4/27 11:01
 */

@Service
@AllArgsConstructor
@Slf4j
public class InsuranceServiceImpl implements IInsuranceService {

    private final DganzeInsuranceMapper dganzeInsuranceMapper;

    private final DgdocAqjdConsSiteMapper dgdocAqjdConsSiteMapper;

    private final DganzeInsuranceServiceRecordMapper insuranceServiceRecordMapper;

    private final AnzeSysDepartMapper anzeSysDepartMapper;

    private final AnzeInsuranceEventRecordMapper insuranceEventRecordMapper;

    private final DganzeInsuranceOrgServiceRecordServiceResultMapper insuranceOrgServiceRecordServiceResultMapper;

    private final DganzeServiceRecordFileMapper dganzeServiceRecordFileMapper;

    @Override
    public IPage<InsuranceOrderListVo> queryInsurancePage(Integer pageNo, Integer pageSize, DgdocInsuranceOrderBo bo) throws BusinessException {
        if (EntType.INSURANCE.equals(LoginUserHelper.GetUserType())) {
            LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            String organizationId = user.getOrganizationId();
            Page<DganzeInsurance> dganzeInsurancePage = dganzeInsuranceMapper.selectPage(new Page<>(pageNo, pageSize), new LambdaQueryWrapper<DganzeInsurance>()
                    .eq(DganzeInsurance::getOrgId, organizationId)
                    .like(!StringUtils.isEmpty(bo.getName()), DganzeInsurance::getProjectName, bo.getName())
                    .like(!StringUtils.isEmpty(bo.getSerialNo()), DganzeInsurance::getSerialNo, bo.getSerialNo())
                    .orderByDesc(DganzeInsurance::getCreateTime));
            List<InsuranceOrderListVo> result = new ArrayList<>();

            dganzeInsurancePage.getRecords().forEach(a -> {
                InsuranceOrderListVo v = new InsuranceOrderListVo();
                v.setProjectId(a.getProjectId());
                v.setUploadTime(a.getCreateTime());
                v.setOrderNo(a.getSerialNo());
                v.setName(a.getProjectName());
                result.add(v);
            });


            IPage<InsuranceOrderListVo> page = new Page<>(dganzeInsurancePage.getCurrent(), dganzeInsurancePage.getSize(), dganzeInsurancePage.getTotal());
            page.setRecords(result);
            return page;

        } else {
            throw new BusinessException("无访问权限");
        }
    }

    @Override
    public IPage<InsuranceRecordListVo> queryServiceRecordPage(Integer pageNo, Integer pageSize, DgOrgServiceQueryBo bo) throws BusinessException {
        if (EntType.ORG.equals(LoginUserHelper.GetUserType())) {
            LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            String organizationId = user.getOrganizationId();

            Page<DganzeInsuranceServiceRecord> dganzeInsurancePage = insuranceServiceRecordMapper.selectPage(new Page<>(pageNo, pageSize),
                    new LambdaQueryWrapper<DganzeInsuranceServiceRecord>()
                            .eq(DganzeInsuranceServiceRecord::getOrgId, organizationId)
                            .like(!StringUtils.isEmpty(bo.getServiceType()), DganzeInsuranceServiceRecord::getServiceType, bo.getServiceType())
                            .like(!StringUtils.isEmpty(bo.getProjectName()), DganzeInsuranceServiceRecord::getProjectName, bo.getProjectName())
                            .orderByDesc(DganzeInsuranceServiceRecord::getServiceTime));

            List<InsuranceRecordListVo> insuranceRecordListVos = BeanUtil.copyToList(dganzeInsurancePage.getRecords(), InsuranceRecordListVo.class);

            IPage<InsuranceRecordListVo> page = new Page<>();
            page.setRecords(insuranceRecordListVos);
            return page;

        } else {
            throw new BusinessException("无访问权限");
        }
    }

    @Override
    public DganzeInsuranceServiceRecordVo queryServiceRecordById(Long id) {
        DganzeInsuranceServiceRecord dganzeInsuranceServiceRecord = insuranceServiceRecordMapper.selectById(id);
        DganzeInsuranceServiceRecordVo dganzeInsuranceServiceRecordVo = BeanUtil.copyProperties(dganzeInsuranceServiceRecord, DganzeInsuranceServiceRecordVo.class);
        List<DganzeInsuranceOrgServiceRecordServiceResult> dganzeInsuranceOrgServiceRecordServiceResults = insuranceOrgServiceRecordServiceResultMapper.selectList(
                new LambdaQueryWrapper<DganzeInsuranceOrgServiceRecordServiceResult>()
                        .eq(DganzeInsuranceOrgServiceRecordServiceResult::getResultId, id)
        );
        dganzeInsuranceServiceRecordVo.setServiceResultList(dganzeInsuranceOrgServiceRecordServiceResults);
        List<DganzeServiceRecordFile> dganzeServiceRecordFiles = dganzeServiceRecordFileMapper.selectList(new LambdaQueryWrapper<DganzeServiceRecordFile>()
                .eq(DganzeServiceRecordFile::getRecordId, id).orderByDesc(DganzeServiceRecordFile::getUpdateTime));

        dganzeInsuranceServiceRecordVo.setServiceReportFilePath
                (dganzeServiceRecordFiles.stream().map(DganzeServiceRecordFile::getFilePath).collect(Collectors.toList()));

        return dganzeInsuranceServiceRecordVo;
    }

    @Override
    public IPage<InsuranceRecordListVo> checkTodoList(Integer pageNo, Integer pageSize) throws BusinessException {
        String townShipId = null;
        switch (LoginUserHelper.GetUserType()) {
//            case EntType.TOWN:
//                LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
//                String organizationId = user.getOrganizationId();
//                AnzeSysDepart anzeSysDepart = anzeSysDepartMapper.selectOne(new LambdaQueryWrapper<AnzeSysDepart>()
//                        .eq(AnzeSysDepart::getOrganizationId, organizationId), false);
//                if (ObjectUtil.isNotNull(anzeSysDepart)) {
//                    if (ObjectUtil.isNotNull(anzeSysDepart.getTownShipId())) {
//                        townShipId = anzeSysDepart.getTownShipId();
//                    } else {
//                        throw new BusinessException("机构没有配置镇街信息");
//                    }
//                } else {
//                    throw new BusinessException("查询机构信息异常！");
//                }
//            case EntType.CITY:
//                return insuranceServiceRecordMapper.queryServiceTodoList(new Page<>(pageNo, pageSize), townShipId, null);
//            default:
//                throw new BusinessException("无访问权限");
            case EntType.INSURANCE:
                //查出保险公司关联的工地
                List<String> siteIds = dganzeInsuranceMapper.selectList(new LambdaQueryWrapper<DganzeInsurance>()
                                .eq(DganzeInsurance::getOrgId, LoginUserHelper.GetOrgId()))
                        .stream().map(DganzeInsurance::getProjectId).collect(Collectors.toList());
                return insuranceServiceRecordMapper.queryServiceTodoList(new Page<>(pageNo, pageSize), null, null, siteIds);
            default:
                throw new BusinessException("无访问权限");
        }
    }

    @Override
    public Boolean deleteById(String id) throws BusinessException {
//        String townShipId = null;
        switch (LoginUserHelper.GetUserType()) {
//            case EntType.TOWN:
//                LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
//                String organizationId = user.getOrganizationId();
//                AnzeSysDepart anzeSysDepart = anzeSysDepartMapper.selectOne(new LambdaQueryWrapper<AnzeSysDepart>()
//                        .eq(AnzeSysDepart::getOrganizationId, organizationId), false);
//                if (ObjectUtil.isNotNull(anzeSysDepart)) {
//                    if (ObjectUtil.isNotNull(anzeSysDepart.getTownShipId())) {
//                        townShipId = anzeSysDepart.getTownShipId();
//                    } else {
//                        throw new BusinessException("无权限！");
//                    }
//                } else {
//                    throw new BusinessException("无权限！");
//                }
//            case EntType.CITY:
//                IPage<InsuranceRecordListVo> hasPowerToDel = insuranceServiceRecordMapper.queryServiceTodoList(new Page<>(1, 1), townShipId, null);
//                if (CollectionUtil.isEmpty(hasPowerToDel.getRecords())) {
//                    throw new BusinessException("无权限！");
//                }
//                boolean b = insuranceServiceRecordMapper.deleteById(id) > 0;
//                insuranceEventRecordMapper.delete(new LambdaQueryWrapper<AnzeInsuranceEventRecord>().eq(AnzeInsuranceEventRecord::getContentId, id));
//                return b;
//            default:
//                throw new BusinessException("无访问权限");
            case EntType.INSURANCE:
                boolean b = insuranceServiceRecordMapper.deleteById(id) > 0;
                insuranceEventRecordMapper.delete(new LambdaQueryWrapper<AnzeInsuranceEventRecord>().eq(AnzeInsuranceEventRecord::getContentId, id));
                return b;
            default:
                throw new BusinessException("无访问权限");
        }
    }

    @Override
    public Boolean auditByVo(InsuranceRecordListVo vo) throws BusinessException {
        DganzeInsuranceServiceRecord dganzeInsuranceServiceRecord = insuranceServiceRecordMapper.selectById(vo.getId());
        if (ObjectUtil.isNotNull(dganzeInsuranceServiceRecord)) {
            dganzeInsuranceServiceRecord.setAuditState(vo.getAuditState());
            boolean b = insuranceServiceRecordMapper.updateById(dganzeInsuranceServiceRecord) == 1;
            if (b && (EntType.INSURANCE.equals(LoginUserHelper.GetUserType()))) {
                AnzeInsuranceEventRecord r = new AnzeInsuranceEventRecord();
                r.setContentId(String.valueOf(dganzeInsuranceServiceRecord.getId()));
                r.setProjectId(dganzeInsuranceServiceRecord.getProjectId());
                r.setCreateTime(new Date());
                r.setRecordTitle(dganzeInsuranceServiceRecord.getProjectName());
                r.setRecordType(InsuranceTypeConstant.TYPE_SERVICE_AUDIT);
                r.setOrgId(LoginUserHelper.GetOrgId());
                r.setUserId(LoginUserHelper.GetUserName());
                r.setRecordStatus(String.valueOf(dganzeInsuranceServiceRecord.getAuditState()));

                insuranceEventRecordMapper.insert(r);
            }
            return b;
        } else {
            throw new BusinessException("数据异常");
        }
    }

    @Override
    public Boolean insertServiceRecordFile(Long id, String fileUrl) {
        DganzeInsuranceOrgServiceRecordServiceResult r = new DganzeInsuranceOrgServiceRecordServiceResult();
        r.setId(id);
        r.setFilePath(fileUrl);

        return insuranceOrgServiceRecordServiceResultMapper.insert(r) > 0;
    }

    @Override
    public Boolean delServiceRecordFile(Long id) {
        return insuranceOrgServiceRecordServiceResultMapper.deleteById(id) > 0;
    }

    @Override
    public IPage<AnzeInsuranceEventRecord> queryEventRecordList(Integer pageNo, Integer pageSize) throws BusinessException {
        IPage<AnzeInsuranceEventRecord> result = null;
        String townShipId = null;
        LambdaQueryWrapper<AnzeInsuranceEventRecord> wrapper = new LambdaQueryWrapper<AnzeInsuranceEventRecord>().orderByDesc(AnzeInsuranceEventRecord::getCreateTime);
        switch (LoginUserHelper.GetUserType()) {
            // 保司视角
            case EntType.INSURANCE:
                // 查出保险公司所有项目ID
                List<String> projectIds = dganzeInsuranceMapper.selectList(
                        new LambdaQueryWrapper<DganzeInsurance>().eq(DganzeInsurance::getOrgId, LoginUserHelper.GetOrgId())
                ).stream().map(DganzeInsurance::getProjectId).collect(Collectors.toList());
                wrapper.and(w ->
                        w.and(w1 -> w1.in(AnzeInsuranceEventRecord::getRecordType,
                                                new Integer[]{
                                                        InsuranceTypeConstant.TYPE_ORDER_FILE_UPLOAD,
                                                        InsuranceTypeConstant.TYPE_ORDER_CHOOSE_ORG,
                                                        InsuranceTypeConstant.TYPE_SERVICE_AUDIT
                                                })
                                        .eq(AnzeInsuranceEventRecord::getOrgId, LoginUserHelper.GetOrgId()))
                                .or(w2 -> w2.eq(AnzeInsuranceEventRecord::getRecordType, InsuranceTypeConstant.TYPE_SERVICE_RECORD)
                                        .in(CollectionUtil.isNotEmpty(projectIds), AnzeInsuranceEventRecord::getProjectId, projectIds))
                );
                break;
            // 服务机构
            case EntType.ORG:
                wrapper.in(AnzeInsuranceEventRecord::getRecordType, new Integer[]{InsuranceTypeConstant.TYPE_SERVICE_RECORD});
                wrapper.eq(AnzeInsuranceEventRecord::getOrgId, LoginUserHelper.GetOrgId());
                break;
            case EntType.TOWN:
                AnzeSysDepart anzeSysDepart = anzeSysDepartMapper.selectOne(new LambdaQueryWrapper<AnzeSysDepart>()
                        .eq(AnzeSysDepart::getOrganizationId, LoginUserHelper.GetOrgId()), false);
                if (ObjectUtil.isNotNull(anzeSysDepart)) {
                    townShipId = anzeSysDepart.getTownShipId();
                }
            case EntType.CITY:
//                IPage<InsuranceRecordListVo> insuranceRecordListVoIPage = insuranceServiceRecordMapper.queryServiceTodoList(new Page<>(pageNo, pageSize), townShipId, null, null);
//                List<String> cityProjectIds = insuranceRecordListVoIPage.getRecords().stream()
//                        .map(InsuranceRecordListVo::getProjectId).collect(Collectors.toList());
                wrapper.in(AnzeInsuranceEventRecord::getRecordType, Arrays.asList(InsuranceTypeConstant.TYPE_SERVICE_RECORD,
                        InsuranceTypeConstant.TYPE_SERVICE_AUDIT, InsuranceTypeConstant.SERVICE_TYPE_OK, InsuranceTypeConstant.TYPE_ORDER_CHOOSE_ORG));
//                if (CollectionUtil.isNotEmpty(cityProjectIds)) {
//                    wrapper.in(AnzeInsuranceEventRecord::getProjectId, cityProjectIds);
//                }
                break;
            default:
                throw new BusinessException("无访问权限");
        }
        result = insuranceEventRecordMapper.selectPage(new Page<>(pageNo, pageSize), wrapper);
        return result;
    }
}


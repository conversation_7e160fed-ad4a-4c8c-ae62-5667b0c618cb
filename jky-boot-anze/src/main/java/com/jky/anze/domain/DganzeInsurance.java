package com.jky.anze.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @description:
 * @author: cq
 * @date: 2025/4/22 17:14
 */

@Data
@TableName("dganze_insurance")
public class DganzeInsurance implements Serializable {

    @TableId("insurance_id")
    private Long insuranceId;

    private String serialNo;

    /**
     * 含税保费
     */
    private Double insuranceMoney;

    private Double serviceMoney;

    /**
     * 累计赔偿限额
     */
    private Double maxMoney;

    private String projectId;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    private String updateBy;

    private String createBy;

    private String orgId;

    private String projectName;

    /**
     * 合同金额
     */
    private Double contractAmount;

    /**
     * 每人死亡限额
     */
    private Double deathLimitPerPerson;

    /**
     * 每人伤残限额
     */
    private Double disabilityLimitPerPerson;

    /**
     * 合同金额偏低状态 0-正常 1-偏低
     */
    private Integer isLowContractAmount;
}

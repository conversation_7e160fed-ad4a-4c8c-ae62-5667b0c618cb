package com.jky.anze.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.anze.constant.EntType;
import com.jky.anze.domain.*;
import com.jky.anze.domain.bo.DgdocPcProjectBo;
import com.jky.anze.domain.bo.RecordResultBo;
import com.jky.anze.domain.vo.DgPcProjectItemVo;
import com.jky.anze.domain.vo.PcProjectDetailBaseVo;
import com.jky.anze.domain.vo.PcProjectDetailVo;
import com.jky.anze.domain.vo.PcProjectOnSiteVo;
import com.jky.anze.mapper.*;
import com.jky.anze.service.IPcProjectService;
import com.jky.dgdoc.constants.UserRoleConstants;
import com.jky.dgdoc.domain.DgdocAqjdConsSite;
import com.jky.dgdoc.mapper.DgdocAqjdConsSiteMapper;
import com.jky.dgdoc.mapper.DgdocProjectMapper;
import com.jky.dgdoc.service.IDgdocAqjdConsSiteService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.online.config.exception.BusinessException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @description:
 * @author: cq
 * @date: 2025/4/30 16:52
 */

@Service
@AllArgsConstructor
@Slf4j
public class PcProjectServiceImpl implements IPcProjectService {

    private final DgdocAqjdConsSiteMapper dgdocAqjdConsSiteMapper;

    private final DganzeInsuranceMapper insuranceMapper;

    private final AnzeInsuranceEventRecordMapper insuranceEventRecordMapper;

    private final DganzeInsuranceServiceRecordMapper dganzeInsuranceServiceRecordMapper;

    private final DganzeServiceRecordFileMapper dganzeServiceRecordFileMapper;
    private final DganzeInsuranceOrgServiceRecordServiceResultMapper insuranceOrgServiceRecordServiceResultMapper;

    private AnzeSysDepartMapper anzeSysDepartMapper;
    private AnzeSysDictMapper anzeSysDictMapper;
    private AnzeSysDictItemMapper anzeSysDictItemMapper;
    private DgdocProjectMapper dgdocProjectMapper;
    private DganzeStateMaintainMapper dganzeStateMaintainMapper;

    private final IDgdocAqjdConsSiteService dgdocAqjdConsSiteService;

    private List<String> WATER_TOWNSHIP_CACHE;

    @Override
    public IPage<DgPcProjectItemVo> queryProjectItemVo(DgdocPcProjectBo bo, Integer pageNo, Integer pageSize) throws BusinessException {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String entType = sysUser.getEntType();
        IPage<DgPcProjectItemVo> result = new Page<>(pageNo, pageSize);
//        boolean joinStateMaintain =
//                (bo.getReportType() != null && bo.getReportType() == 1)
//                        || (bo.getHasInsuranceOrder() != null && bo.getHasInsuranceOrder() == 1)
//                        || (bo.getHasOrg() != null && bo.getHasOrg() == 1);

        QueryWrapper<DgPcProjectItemVo> queryWrapper = new QueryWrapper<DgPcProjectItemVo>()
                .like(!StringUtils.isEmpty(bo.getProjectName()), "n.project_name", bo.getProjectName())
                .like(!StringUtils.isEmpty(bo.getConsSiteName()), "m.cons_site_name", bo.getConsSiteName())
                .and(bo.getReportType() != null && bo.getReportType() == 0, wrapper -> wrapper.eq("s.report_status", 0).or().isNull("s.report_status"))
                .eq(bo.getReportType() != null && bo.getReportType() == 1, "s.report_status", 1)
                .and(bo.getHasInsuranceOrder() != null && bo.getHasInsuranceOrder() == 0, wrapper -> wrapper.eq("s.has_insurance_order", 0).or().isNull("s.has_insurance_order"))
                .eq(bo.getHasInsuranceOrder() != null && bo.getHasInsuranceOrder() == 1, "s.has_insurance_order", 1)
                .and(bo.getHasOrg() != null && bo.getHasOrg() == 0, wrapper -> wrapper.eq("s.has_org", 0).or().isNull("s.has_org"))
                .eq(bo.getHasOrg() != null && bo.getHasOrg() == 1, "s.has_org", 1)
                .eq("m.project_status", 0);
        Set<String> userRolesSet = dgdocProjectMapper.getUserRolesSet(sysUser.getUsername());

        if (EntType.CITY.equals(entType) || userRolesSet.contains(UserRoleConstants.ADMIN)) {
            result = insuranceMapper.queryPcProjectPage(new Page<>(pageNo, pageSize), queryWrapper);
        } else if (EntType.TOWN.equals(entType)) {
            QueryWrapper<AnzeSysDepart> departWrapper = new QueryWrapper<>();
            departWrapper.eq("organization_id", sysUser.getOrganizationId());
            AnzeSysDepart depart = anzeSysDepartMapper.selectOne(departWrapper);
            if (this.getWaterTownShipIds().contains(depart.getTownShipId())) {
                queryWrapper.in("m.township_id", WATER_TOWNSHIP_CACHE);
            } else {
                queryWrapper.eq("m.township_id", depart.getTownShipId());
            }
            result = insuranceMapper.queryPcProjectPage(new Page<>(pageNo, pageSize), queryWrapper);
        } else if (EntType.ORG.equals(entType)) {
            queryWrapper.eq(StringUtils.isNotBlank(sysUser.getOrganizationId()), "org.org_id", sysUser.getOrganizationId());
            result = insuranceMapper.selectConsSiteWithOrg(new Page<>(pageNo, pageSize), queryWrapper);
        } else if (EntType.INSURANCE.equals(entType)) {
            if (StringUtils.isNotBlank(sysUser.getOrganizationId())) {
                if (Objects.nonNull(bo.getInsuranceProject()) && bo.getInsuranceProject() == 1) {
                    queryWrapper.and(wrapper -> wrapper
                            .eq("ins.org_id", sysUser.getOrganizationId())
                    );
                } else {
                    if (StringUtils.isNotBlank(bo.getProjectName()) || StringUtils.isNotBlank(bo.getConsSiteName())
                            || ObjectUtil.isNotNull(bo.getReportType()) || ObjectUtil.isNotNull(bo.getHasInsuranceOrder())
                            || ObjectUtil.isNotNull(bo.getHasOrg())) {
                        queryWrapper.and(wrapper -> wrapper
                                .eq("ins.org_id", sysUser.getOrganizationId())
                                .or()
                                .isNull("ins.insurance_id")
                        );
                    } else {
                        return result;
                    }
                }
            }
            result = insuranceMapper.selectConsSiteWithInsurance(new Page<>(pageNo, pageSize), queryWrapper);
        } else {
            throw new BusinessException("无访问权限");
        }


        // 补充信息
        if (result != null && !result.getRecords().isEmpty()) {
            result.getRecords().forEach(a -> {
                Long count = dganzeInsuranceServiceRecordMapper.selectCount(new LambdaQueryWrapper<DganzeInsuranceServiceRecord>()
                        .eq(DganzeInsuranceServiceRecord::getProjectId, a.getId()));
                a.setServiceCount(count.intValue());

                if (a.getHasInsuranceOrder() == null) {
                    a.setHasInsuranceOrder(0);
                }
                if (a.getHasOrg() == null) {
                    a.setHasOrg(0);
                }
                if (a.getReportType() == null) {
                    a.setReportType(0);
                }
            });
        }

        return result;
    }

    @Override
    public Map<Integer, List<PcProjectDetailVo>> eventRecordList(String id) {
        List<DganzeInsuranceServiceRecord> anzeInsuranceEventRecords = dganzeInsuranceServiceRecordMapper.selectList(new LambdaQueryWrapper<DganzeInsuranceServiceRecord>()
                .eq(DganzeInsuranceServiceRecord::getProjectId, id));

        DgdocAqjdConsSite dgdocAqjdConsSite = dgdocAqjdConsSiteMapper.selectById(id);

        Map<Integer, List<DganzeInsuranceServiceRecord>> collect = anzeInsuranceEventRecords.stream().collect(Collectors.groupingBy(DganzeInsuranceServiceRecord::getServiceType));

        Map<Integer, List<PcProjectDetailVo>> result = new HashMap<>();

        collect.forEach((k, v) -> {
            if (!result.containsKey(k)) {
                result.put(k, new ArrayList<>());
            }
            v.forEach(v1 -> {
                // 获取服务报告路径
                DganzeServiceRecordFile recordFile = dganzeServiceRecordFileMapper.selectOne(
                        new LambdaQueryWrapper<DganzeServiceRecordFile>()
                                .eq(DganzeServiceRecordFile::getRecordId, v1.getId())
                                .last("LIMIT 1")
                );
                PcProjectDetailVo vo = new PcProjectDetailVo();
                vo.setId(String.valueOf(v1.getId()));
                vo.setBase(new PcProjectDetailBaseVo());
                vo.getBase().setLocation(v1.getLocation());
                vo.getBase().setLocationName(dgdocAqjdConsSite.getConsSiteAddr());
                vo.getBase().setProjectName(dgdocAqjdConsSite.getConsSiteName());
                vo.getBase().setServiceTime(v1.getServiceTime());
                vo.getBase().setServiceName(v1.getServicePersonnel());
                if (recordFile != null) {
                    vo.getBase().setReportFilePath(recordFile.getFilePath());
                }

                //TODO 出入场照片后续对接
                PcProjectOnSiteVo inPhote = new PcProjectOnSiteVo();
                inPhote.setFilePath(v1.getEntryExitPhoto());
                inPhote.setType(1);
                vo.getSitePhotos().add(inPhote);

//                PcProjectOnSiteVo outPhote = new PcProjectOnSiteVo();
//                outPhote.setFilePath(v1.getEntryExitPhoto());
//                outPhote.setType(2);
//                vo.getSitePhotos().add(outPhote);


                // 现场照片，现在还是一张
                PcProjectOnSiteVo locationPhoto = new PcProjectOnSiteVo();
                locationPhoto.setFilePath(v1.getScenePhoto());
                locationPhoto.setType(3);
                vo.getLocationPhotos().add(locationPhoto);
                result.get(k).add(vo);
                // 人员资质
                PcProjectOnSiteVo personal = new PcProjectOnSiteVo();
                personal.setFilePath(v1.getPersonnelQualification());
                personal.setType(4);
                vo.getIdentityPhotos().add(personal);

                // 服务成果
                List<DganzeInsuranceOrgServiceRecordServiceResult> dganzeInsuranceOrgServiceRecordServiceResults = insuranceOrgServiceRecordServiceResultMapper.selectList(
                        new LambdaQueryWrapper<DganzeInsuranceOrgServiceRecordServiceResult>()
                                .eq(DganzeInsuranceOrgServiceRecordServiceResult::getResultId, v1.getId())
                );

                dganzeInsuranceOrgServiceRecordServiceResults.forEach(a -> {
                    PcProjectOnSiteVo record = new PcProjectOnSiteVo();
                    record.setType(5);
                    record.setFilePath(a.getFilePath());

                    vo.getResultPhotos().add(record);
                });

            });
        });

        return result;

    }

    @Override
    public Boolean uploadResult(RecordResultBo bo) throws BusinessException {
        DganzeInsuranceServiceRecord dganzeInsuranceServiceRecord = dganzeInsuranceServiceRecordMapper.selectById(bo.getId());
        if (ObjectUtil.isNull(dganzeInsuranceServiceRecord)) {
            throw new BusinessException("数据异常");
        }
        DganzeServiceRecordFile file = new DganzeServiceRecordFile();
        file.setFilePath(bo.getFilePath());
        file.setRecordId(bo.getId());
        file.setProjectId(dganzeInsuranceServiceRecord.getProjectId());

        // 更新工地表的report_status字段
        dganzeStateMaintainMapper.upsertReportStatus(dganzeInsuranceServiceRecord.getProjectId());

        return dganzeServiceRecordFileMapper.insert(file) > 0;
    }

    private List<String> getWaterTownShipIds() {
        if (WATER_TOWNSHIP_CACHE != null) {
            return WATER_TOWNSHIP_CACHE; // 如果缓存已存在，直接返回
        }

        // 查询字典ID
        String dictId = anzeSysDictMapper.selectOne(new LambdaQueryWrapper<SysDict>()
                .eq(SysDict::getDictCode, "dgzjjsp_project_town_id")).getId();

        // 查询字典项并提取 item_value 列表
        List<SysDictItem> dictItems = anzeSysDictItemMapper.selectList(
                new LambdaQueryWrapper<SysDictItem>()
                        .eq(SysDictItem::getDictId, dictId)
        );

        // 模糊匹配 WATER_TOWNS 中的元素
        WATER_TOWNSHIP_CACHE = dictItems.stream()
                .filter(item -> Stream.of("麻涌", "望牛墩", "道滘", "洪梅", "中堂").anyMatch(waterTown -> item.getItemText().contains(waterTown)))
                .map(SysDictItem::getItemValue)
                .collect(Collectors.toList());

        return WATER_TOWNSHIP_CACHE; // 返回缓存结果
    }
}

package com.jky.anze.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @description:
 * @author: cq
 * @date: 2025/4/27 14:30
 */
@Data
@ApiModel("服务机构查询列表")
public class InsuranceRecordListVo {

    @ApiModelProperty("项目id")
    private String projectId;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("用于查询详情的记录id")
    private String id;

    @ApiModelProperty("审核状态：0：审核中，1：审核通过，2：驳回")
    private Integer auditState;

    @ApiModelProperty("服务日期")
    private Date serviceDate;

    @ApiModelProperty("服务类型：0：技术咨询，1：应急演练，2：安全培训，3：现场巡查")
    private Integer serviceType;
}


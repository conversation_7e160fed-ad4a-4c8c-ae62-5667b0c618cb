package com.jky.anze.domain.vo;

import lombok.Data;

@Data
public class DashboardStatsVo {
    private Integer serviceProjects; // 已开展服务项目
    private Integer unserviceProjects; // 未开展服务项目
    private Integer insuranceCnt; // 保单数
    private Double insuranceMoney; // 保险金额
    private Integer serviceTimes; // 服务次数
    private Integer techConsultCount; // 技术咨询
    private Integer emergencyDrillCount; // 应急演练
    private Integer safetyTrainingCount; // 安全培训
    private Integer siteInspectionCount; // 现场巡查
    private Integer riskAssessmentCount; // 风险评估
    private Integer standardConstructionCount; // 标准建设
    private Integer otherServicesCount; // 其他服务
}
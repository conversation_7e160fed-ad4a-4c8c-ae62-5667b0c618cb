package com.jky.anze.mapper;

import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DashboardStatsMapper {
    Integer countTotalProjects(@Param("insuranceOrgId") String insuranceOrgId, @Param("serviceOrgId") String serviceOrgId, @Param("townshipIds") List<String> townshipIds);

    Integer countServiceProjects(@Param("insuranceOrgId") String insuranceOrgId, @Param("serviceOrgId") String serviceOrgId, @Param("townshipIds") List<String> townshipIds);

    Integer countInsuranceCnt(@Param("insuranceOrgId") String insuranceOrgId, @Param("serviceOrgId") String serviceOrgId, @Param("townshipIds") List<String> townshipIds, @Param("startTime") String startTime, @Param("endTime") String endTime);

    Double sumInsuranceMoney(@Param("insuranceOrgId") String insuranceOrgId, @Param("serviceOrgId") String serviceOrgId, @Param("townshipIds") List<String> townshipIds, @Param("startTime") String startTime, @Param("endTime") String endTime);

    Integer countServiceTimes(@Param("insuranceOrgId") String insuranceOrgId, @Param("serviceOrgId") String serviceOrgId, @Param("townshipIds") List<String> townshipIds, @Param("startTime") String startTime, @Param("endTime") String endTime);

    Integer countNewInsuranceCnt(@Param("insuranceOrgId") String insuranceOrgId, @Param("serviceOrgId") String serviceOrgId, @Param("townshipIds") List<String> townshipIds, @Param("startTime") String startTime, @Param("endTime") String endTime);

    Double sumNewInsuranceMoney(@Param("insuranceOrgId") String insuranceOrgId, @Param("serviceOrgId") String serviceOrgId, @Param("townshipIds") List<String> townshipIds, @Param("startTime") String startTime, @Param("endTime") String endTime);

    Integer countNewServiceTimes(@Param("insuranceOrgId") String insuranceOrgId, @Param("serviceOrgId") String serviceOrgId, @Param("townshipIds") List<String> townshipIds, @Param("startTime") String startTime, @Param("endTime") String endTime);

    Integer countServiceType(@Param("insuranceOrgId") String insuranceOrgId, @Param("serviceOrgId") String serviceOrgId, @Param("townshipIds") List<String> townshipIds, @Param("serviceType") Integer serviceType, @Param("startTime") String startTime, @Param("endTime") String endTime);
}
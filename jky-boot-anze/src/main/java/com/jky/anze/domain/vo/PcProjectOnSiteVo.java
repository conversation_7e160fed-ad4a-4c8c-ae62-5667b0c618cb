package com.jky.anze.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @description:
 * @author: cq
 * @date: 2025/5/6 11:57
 */
@Data
@ApiModel("出入场照片信息")
public class PcProjectOnSiteVo {

    @ApiModelProperty("照片")
    private String filePath;

    @ApiModelProperty("时间")
    private Date uploadTime;

    @ApiModelProperty("类型：1：入场 2：出场 3：现场照片 4：人员资质 5：服务结果")
    private Integer type;
}


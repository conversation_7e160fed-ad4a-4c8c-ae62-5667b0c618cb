package com.jky.anze.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @description:
 * @author: cq
 * @date: 2025/5/6 11:51
 */
@Data
public class PcProjectDetailBaseVo {

    @ApiModelProperty("工地名称")
    private String locationName;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("服务时间")
    @JsonFormat(pattern = "yyyy年MM月dd日 HH:mm:ss")
    private Date serviceTime;

    @ApiModelProperty("定位")
    private String location;


    @ApiModelProperty("服务人员")
    private String serviceName;

    @ApiModelProperty("服务报告路径")
    private String reportFilePath;
}


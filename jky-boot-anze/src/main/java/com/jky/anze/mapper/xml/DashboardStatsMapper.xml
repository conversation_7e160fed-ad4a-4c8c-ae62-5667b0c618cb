<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jky.anze.mapper.DashboardStatsMapper">

    <!-- 工地总数 -->
    <select id="countTotalProjects" resultType="int">
        SELECT COUNT(1)
        FROM dgdoc_aqjd_cons_site site
        <where>
            site.project_status = 0
            <if test="insuranceOrgId != null">
                AND site.id IN (
                SELECT project_id
                FROM dganze_insurance
                WHERE org_id = #{insuranceOrgId}
                )
            </if>
            <if test="serviceOrgId != null">
                AND site.id IN (
                SELECT DISTINCT org.project_id
                FROM dganze_insurance_org org
                WHERE org.org_id = #{serviceOrgId}
                )
            </if>
            <if test="townshipIds != null and townshipIds.size > 0">
                AND site.township_id IN
                <foreach collection="townshipIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <!-- 已开展项目 -->
    <select id="countServiceProjects" resultType="int">
        SELECT COUNT(DISTINCT site.id)
        FROM dgdoc_aqjd_cons_site site
        JOIN dganze_insurance_service_record sr ON sr.project_id = site.id
        <where>
            sr.audit_state = 1 and site.project_status = 0
            <if test="insuranceOrgId != null">
                AND site.id IN (
                SELECT project_id
                FROM dganze_insurance
                WHERE org_id = #{insuranceOrgId}
                )
            </if>
            <if test="serviceOrgId != null">
                AND site.id IN (
                SELECT DISTINCT sr.project_id
                FROM dganze_insurance_service_record sr
                WHERE sr.org_id = #{serviceOrgId}
                )
            </if>
            <if test="townshipIds != null and townshipIds.size > 0">
                AND site.township_id IN
                <foreach collection="townshipIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <!-- 保单数量 -->
    <select id="countInsuranceCnt" resultType="int">
        SELECT COUNT(DISTINCT ins.insurance_id)
        FROM dganze_insurance ins
        <where>
            <if test="insuranceOrgId != null">
                AND ins.org_id = #{insuranceOrgId}
            </if>

            <if test="serviceOrgId != null">
                AND ins.project_id IN (
                SELECT DISTINCT sr.project_id
                FROM dganze_insurance_service_record sr
                WHERE sr.org_id = #{serviceOrgId}
                )
            </if>

            <if test="townshipIds != null and townshipIds.size > 0">
                AND ins.project_id IN (
                SELECT DISTINCT site.id
                FROM dgdoc_aqjd_cons_site site
                WHERE site.township_id IN
                <foreach collection="townshipIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
                )
            </if>
            <if test="startTime != null and startTime != ''">
                AND ins.create_time &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                AND ins.create_time &lt;= #{endTime}
            </if>
        </where>
    </select>

    <!-- 保额总金额 -->
    <select id="sumInsuranceMoney" resultType="double">
        SELECT IFNULL(SUM(ins.insurance_money), 0)
        FROM dganze_insurance ins
        <where>
            <if test="insuranceOrgId != null">
                AND ins.org_id = #{insuranceOrgId}
            </if>

            <if test="serviceOrgId != null">
                AND ins.project_id IN (
                SELECT DISTINCT sr.project_id
                FROM dganze_insurance_service_record sr
                WHERE sr.org_id = #{serviceOrgId}
                )
            </if>

            <if test="townshipIds != null and townshipIds.size > 0">
                AND ins.project_id IN (
                SELECT DISTINCT site.id
                FROM dgdoc_aqjd_cons_site site
                WHERE site.township_id IN
                <foreach collection="townshipIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
                )
            </if>
            <if test="startTime != null and startTime != ''">
                AND ins.create_time &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                AND ins.create_time &lt;= #{endTime}
            </if>
        </where>
    </select>

    <!-- 服务次数 -->
    <select id="countServiceTimes" resultType="int">
        SELECT COUNT(1)
        FROM dganze_insurance_service_record sr
        <where>
            sr.audit_state = 1
            <if test="insuranceOrgId != null">
                AND sr.project_id IN (
                SELECT project_id
                FROM dganze_insurance
                WHERE org_id = #{insuranceOrgId}
                )
            </if>
            <if test="serviceOrgId != null">
                AND sr.org_id = #{serviceOrgId}
            </if>
            <if test="townshipIds != null and townshipIds.size > 0">
                AND sr.project_id IN (
                SELECT id
                FROM dgdoc_aqjd_cons_site
                WHERE township_id IN
                <foreach collection="townshipIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
                )
            </if>
            <if test="startTime != null and startTime != ''">
                AND sr.created_time &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                AND sr.created_time &lt;= #{endTime}
            </if>
        </where>
    </select>

    <!-- 各服务类型次数统计 -->
    <select id="countServiceType" resultType="int">
        SELECT COUNT(1)
        FROM dganze_insurance_service_record sr
        <where>
            sr.audit_state = 1
            AND sr.service_type = #{serviceType}
            <if test="insuranceOrgId != null">
                AND sr.project_id IN (
                SELECT project_id
                FROM dganze_insurance
                WHERE org_id = #{insuranceOrgId}
                )
            </if>
            <if test="serviceOrgId != null">
                AND sr.org_id = #{serviceOrgId}
            </if>
            <if test="townshipIds != null and townshipIds.size &gt; 0">
                AND sr.project_id IN (
                SELECT id
                FROM dgdoc_aqjd_cons_site
                WHERE township_id IN
                <foreach collection="townshipIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
                )
            </if>
            <if test="startTime != null and startTime != ''">
                AND sr.created_time &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                AND sr.created_time &lt;= #{endTime}
            </if>
        </where>
    </select>

</mapper>
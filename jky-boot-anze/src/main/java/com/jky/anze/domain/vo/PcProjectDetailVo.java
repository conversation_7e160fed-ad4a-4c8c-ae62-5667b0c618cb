package com.jky.anze.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: cq
 * @date: 2025/5/6 11:51
 */
@Data
public class PcProjectDetailVo {


    @ApiModelProperty("业务id")
    private String id;

    @ApiModelProperty("基础信息")
    private PcProjectDetailBaseVo base;


    @ApiModelProperty("出入场照片")
    private List<PcProjectOnSiteVo> sitePhotos = new ArrayList<>();

    @ApiModelProperty("现场照片")
    private List<PcProjectOnSiteVo> locationPhotos = new ArrayList<>();

    @ApiModelProperty("人员资质")
    private List<PcProjectOnSiteVo> identityPhotos = new ArrayList<>();

    @ApiModelProperty("服务成果")
    private List<PcProjectOnSiteVo> resultPhotos = new ArrayList<>();
}


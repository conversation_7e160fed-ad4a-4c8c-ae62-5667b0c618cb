package com.jky.dgdoc.service;

import com.jky.dgdoc.domain.DgdocArchivesInstance;
import com.jky.dgdoc.enums.UploadState;
import com.jky.dgdoc.mapper.DgdocArchivesInstanceMapper;
import com.jky.dgdoc.service.impl.DgdocArchivesInstanceServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * removeFile方法测试类
 * 用于验证删除文件后父节点状态是否正确更新
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class RemoveFileTest {

    @Autowired
    private IDgdocArchivesInstanceService archivesInstanceService;

    @Autowired
    private DgdocArchivesInstanceMapper archivesInstanceMapper;

    /**
     * 测试删除子节点文件后父节点状态更新
     */
    @Test
    public void testRemoveFileUpdatesParentState() {
        // 准备测试数据
        String projectMonomerId = "TEST_PROJECT_001";
        String archivesType = "1";
        
        // 创建父节点
        DgdocArchivesInstance parentInstance = new DgdocArchivesInstance();
        parentInstance.setArchivesInstanceId("PARENT_001");
        parentInstance.setArchivesId("PARENT_ARCHIVES_001");
        parentInstance.setProjectMonomerId(projectMonomerId);
        parentInstance.setArchivesType(archivesType);
        parentInstance.setUploadState(UploadState.UPLOADED.getCode());
        parentInstance.setStatus("0"); // 正常状态
        archivesInstanceMapper.insert(parentInstance);
        
        // 创建子节点1（已上传）
        DgdocArchivesInstance childInstance1 = new DgdocArchivesInstance();
        childInstance1.setArchivesInstanceId("CHILD_001");
        childInstance1.setArchivesId("CHILD_ARCHIVES_001");
        childInstance1.setParentId("PARENT_ARCHIVES_001");
        childInstance1.setProjectMonomerId(projectMonomerId);
        childInstance1.setArchivesType(archivesType);
        childInstance1.setUploadState(UploadState.UPLOADED.getCode());
        childInstance1.setStatus("0");
        childInstance1.setFileUrl("http://example.com/file1.pdf");
        archivesInstanceMapper.insert(childInstance1);
        
        // 创建子节点2（已上传）
        DgdocArchivesInstance childInstance2 = new DgdocArchivesInstance();
        childInstance2.setArchivesInstanceId("CHILD_002");
        childInstance2.setArchivesId("CHILD_ARCHIVES_002");
        childInstance2.setParentId("PARENT_ARCHIVES_001");
        childInstance2.setProjectMonomerId(projectMonomerId);
        childInstance2.setArchivesType(archivesType);
        childInstance2.setUploadState(UploadState.UPLOADED.getCode());
        childInstance2.setStatus("0");
        childInstance2.setFileUrl("http://example.com/file2.pdf");
        archivesInstanceMapper.insert(childInstance2);
        
        // 验证初始状态：父节点应该是已上传状态
        DgdocArchivesInstance initialParent = archivesInstanceMapper.selectById("PARENT_001");
        assertEquals(UploadState.UPLOADED.getCode(), initialParent.getUploadState(), "初始状态：父节点应该是已上传");
        
        // 执行删除操作：删除子节点1的文件
        Boolean result = archivesInstanceService.removeFile("CHILD_001");
        assertTrue(result, "删除操作应该成功");
        
        // 验证子节点1的状态已更新
        DgdocArchivesInstance updatedChild1 = archivesInstanceMapper.selectById("CHILD_001");
        assertEquals(UploadState.UNUPLOAD.getCode(), updatedChild1.getUploadState(), "子节点1应该变为未上传状态");
        assertNull(updatedChild1.getFileUrl(), "子节点1的文件URL应该被清空");
        
        // 等待异步处理完成（如果启用了异步处理）
        try {
            Thread.sleep(2000); // 等待2秒
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 验证父节点状态已更新：应该变为部分上传状态
        DgdocArchivesInstance updatedParent = archivesInstanceMapper.selectById("PARENT_001");
        assertEquals(UploadState.PARTUPLOAD.getCode(), updatedParent.getUploadState(), 
                "父节点应该变为部分上传状态（因为还有一个子节点是已上传状态）");
        
        // 继续删除子节点2的文件
        Boolean result2 = archivesInstanceService.removeFile("CHILD_002");
        assertTrue(result2, "第二次删除操作应该成功");
        
        // 再次等待异步处理完成
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 验证父节点状态：应该变为未上传状态
        DgdocArchivesInstance finalParent = archivesInstanceMapper.selectById("PARENT_001");
        assertEquals(UploadState.UNUPLOAD.getCode(), finalParent.getUploadState(), 
                "父节点应该变为未上传状态（因为所有子节点都是未上传状态）");
    }

    /**
     * 测试删除单个子节点不影响其他子节点
     */
    @Test
    public void testRemoveFileDoesNotAffectOtherChildren() {
        // 准备测试数据
        String projectMonomerId = "TEST_PROJECT_002";
        String archivesType = "1";
        
        // 创建两个具有相同archivesId但不同archivesInstanceId的子节点
        DgdocArchivesInstance childInstance1 = new DgdocArchivesInstance();
        childInstance1.setArchivesInstanceId("CHILD_003");
        childInstance1.setArchivesId("SAME_ARCHIVES_ID");
        childInstance1.setProjectMonomerId(projectMonomerId);
        childInstance1.setArchivesType(archivesType);
        childInstance1.setUploadState(UploadState.UPLOADED.getCode());
        childInstance1.setStatus("0");
        childInstance1.setFileUrl("http://example.com/file3.pdf");
        archivesInstanceMapper.insert(childInstance1);
        
        DgdocArchivesInstance childInstance2 = new DgdocArchivesInstance();
        childInstance2.setArchivesInstanceId("CHILD_004");
        childInstance2.setArchivesId("SAME_ARCHIVES_ID");
        childInstance2.setProjectMonomerId(projectMonomerId);
        childInstance2.setArchivesType(archivesType);
        childInstance2.setUploadState(UploadState.UPLOADED.getCode());
        childInstance2.setStatus("0");
        childInstance2.setFileUrl("http://example.com/file4.pdf");
        archivesInstanceMapper.insert(childInstance2);
        
        // 删除第一个子节点的文件
        Boolean result = archivesInstanceService.removeFile("CHILD_003");
        assertTrue(result, "删除操作应该成功");
        
        // 验证第一个子节点状态已更新
        DgdocArchivesInstance updatedChild1 = archivesInstanceMapper.selectById("CHILD_003");
        assertEquals(UploadState.UNUPLOAD.getCode(), updatedChild1.getUploadState(), "第一个子节点应该变为未上传状态");
        assertNull(updatedChild1.getFileUrl(), "第一个子节点的文件URL应该被清空");
        
        // 验证第二个子节点状态未受影响
        DgdocArchivesInstance unchangedChild2 = archivesInstanceMapper.selectById("CHILD_004");
        assertEquals(UploadState.UPLOADED.getCode(), unchangedChild2.getUploadState(), "第二个子节点状态应该保持不变");
        assertEquals("http://example.com/file4.pdf", unchangedChild2.getFileUrl(), "第二个子节点的文件URL应该保持不变");
    }
}

package com.jky.dgdoc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jky.dgdoc.domain.DgdocArchivesInstance;
import com.jky.dgdoc.enums.ArchivesStatus;
import com.jky.dgdoc.enums.AuditState;
import com.jky.dgdoc.enums.UploadState;
import com.jky.dgdoc.mapper.DgdocArchivesInstanceMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 异步档案状态计算服务
 * 用于处理耗时的父节点状态计算操作，避免阻塞主流程
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AsyncArchivesStateService {

    private final DgdocArchivesInstanceMapper archivesInstanceMapper;

    /**
     * 异步计算父节点状态
     *
     * @param archivesInstanceId 档案实例ID
     * @param archivesId 档案ID
     * @param projectMonomerId 项目单体ID
     * @param archivesType 档案类型
     * @param uploadState 上传状态
     * @param auditState 审核状态
     * @return CompletableFuture<Void>
     */
    @Async("archivesTaskExecutor")
    @Transactional(rollbackFor = Exception.class)
    public CompletableFuture<Void> computeParentStateAsync(String archivesInstanceId, String archivesId, String projectMonomerId,
                                                          String archivesType, String uploadState, String auditState) {
        try {
            log.info("开始异步计算父节点状态: archivesInstanceId={}, archivesId={}, projectMonomerId={}, archivesType={}",
                    archivesInstanceId, archivesId, projectMonomerId, archivesType);

            long startTime = System.currentTimeMillis();

            // 执行优化后的父节点状态计算
            computeParentStateOptimized(archivesInstanceId, archivesId, projectMonomerId, archivesType, uploadState, auditState);

            long endTime = System.currentTimeMillis();
            log.info("异步计算父节点状态完成，耗时: {}ms", endTime - startTime);

        } catch (Exception e) {
            log.error("异步计算父节点状态失败: archivesInstanceId={}, archivesId={}, error={}", archivesInstanceId, archivesId, e.getMessage(), e);
            throw e;
        }
        
        return CompletableFuture.completedFuture(null);
    }

    /**
     * 批量异步计算多个档案的父节点状态
     *
     * @param stateUpdateRequests 状态更新请求列表
     * @return CompletableFuture<Void>
     */
    @Async("archivesTaskExecutor")
    @Transactional(rollbackFor = Exception.class)
    public CompletableFuture<Void> batchComputeParentStateAsync(List<StateUpdateRequest> stateUpdateRequests) {
        try {
            log.info("开始批量异步计算父节点状态，数量: {}", stateUpdateRequests.size());
            
            long startTime = System.currentTimeMillis();
            
            // 按项目和档案类型分组，减少数据库查询次数
            Map<String, List<StateUpdateRequest>> groupedRequests = stateUpdateRequests.stream()
                    .collect(Collectors.groupingBy(req -> req.getProjectMonomerId() + "_" + req.getArchivesType()));
            
            for (List<StateUpdateRequest> requests : groupedRequests.values()) {
                if (!requests.isEmpty()) {
                    StateUpdateRequest firstRequest = requests.get(0);
                    // 批量处理同一项目同一类型的档案
                    batchProcessSameTypeArchives(requests, firstRequest.getProjectMonomerId(), firstRequest.getArchivesType());
                }
            }
            
            long endTime = System.currentTimeMillis();
            log.info("批量异步计算父节点状态完成，耗时: {}ms", endTime - startTime);
            
        } catch (Exception e) {
            log.error("批量异步计算父节点状态失败: error={}", e.getMessage(), e);
            throw e;
        }
        
        return CompletableFuture.completedFuture(null);
    }

    /**
     * 批量处理同一项目同一类型的档案状态更新
     */
    private void batchProcessSameTypeArchives(List<StateUpdateRequest> requests, String projectMonomerId, String archivesType) {
        // 获取所有涉及的档案ID
        List<String> archivesIds = requests.stream()
                .map(StateUpdateRequest::getArchivesId)
                .collect(Collectors.toList());
        
        // 批量查询所有相关档案实例
        LambdaQueryWrapper<DgdocArchivesInstance> query = Wrappers.lambdaQuery();
        query.eq(DgdocArchivesInstance::getProjectMonomerId, projectMonomerId);
        query.eq(DgdocArchivesInstance::getArchivesType, archivesType);
        query.in(DgdocArchivesInstance::getArchivesId, archivesIds);
        
        List<DgdocArchivesInstance> instances = archivesInstanceMapper.selectList(query);
        Map<String, DgdocArchivesInstance> instanceMap = instances.stream()
                .collect(Collectors.toMap(DgdocArchivesInstance::getArchivesId, instance -> instance));
        
        // 处理每个更新请求
        for (StateUpdateRequest request : requests) {
            DgdocArchivesInstance instance = instanceMap.get(request.getArchivesId());
            if (instance != null) {
                computeParentStateOptimized(instance.getArchivesInstanceId(), request.getArchivesId(), projectMonomerId, archivesType,
                                          request.getUploadState(), request.getAuditState());
            }
        }
    }

    /**
     * 优化的父节点状态计算方法（从主服务类复制）
     */
    private void computeParentStateOptimized(String archivesInstanceId, String archivesId, String projectMonomerId, String archivesType,
                                           String uploadState, String auditState) {
        // 1. 通过archivesInstanceId获取当前节点信息（避免查询到错误的记录）
        DgdocArchivesInstance currentInstance = archivesInstanceMapper.selectById(archivesInstanceId);
        if (currentInstance == null) {
            log.warn("找不到档案实例: archivesInstanceId={}", archivesInstanceId);
            return;
        }

        // 验证参数一致性
        if (!archivesId.equals(currentInstance.getArchivesId()) ||
            !projectMonomerId.equals(currentInstance.getProjectMonomerId()) ||
            !archivesType.equals(currentInstance.getArchivesType())) {
            log.warn("参数不一致: archivesInstanceId={}, 期望archivesId={}, 实际archivesId={}",
                    archivesInstanceId, archivesId, currentInstance.getArchivesId());
            return;
        }

        // 注意：当前节点的状态已经在主方法中更新过了，这里不需要再次更新

        // 2. 批量查询所有相关的档案实例数据，避免递归查询
        if (StringUtils.isBlank(currentInstance.getParentId())) {
            return;
        }

        // 构建父节点路径列表（从当前节点向上）
        List<String> parentPath = buildParentPath(currentInstance, projectMonomerId, archivesType);
        if (parentPath.isEmpty()) {
            return;
        }

        // 3. 批量查询所有需要的数据
        LambdaQueryWrapper<DgdocArchivesInstance> batchQuery = Wrappers.lambdaQuery();
        batchQuery.eq(DgdocArchivesInstance::getProjectMonomerId, projectMonomerId);
        batchQuery.eq(DgdocArchivesInstance::getArchivesType, archivesType);
        batchQuery.eq(DgdocArchivesInstance::getStatus, ArchivesStatus.NORMAL.getCode());
        // 查询所有可能涉及的节点（包括父节点路径上的节点及其子节点）
        batchQuery.and(wrapper -> {
            wrapper.in(DgdocArchivesInstance::getArchivesId, parentPath)
                   .or()
                   .in(DgdocArchivesInstance::getParentId, parentPath);
        });

        List<DgdocArchivesInstance> allInstances = archivesInstanceMapper.selectList(batchQuery);
        
        // 4. 按父节点分组
        Map<String, List<DgdocArchivesInstance>> parentChildrenMap = allInstances.stream()
                .filter(instance -> StringUtils.isNotBlank(instance.getParentId()))
                .collect(Collectors.groupingBy(DgdocArchivesInstance::getParentId));

        // 5. 从下往上批量更新父节点状态
        List<DgdocArchivesInstance> toUpdate = new ArrayList<>();
        
        for (String parentId : parentPath) {
            List<DgdocArchivesInstance> children = parentChildrenMap.get(parentId);
            if (children == null || children.isEmpty()) {
                continue;
            }

            // 找到父节点实例
            DgdocArchivesInstance parentInstance = allInstances.stream()
                    .filter(instance -> parentId.equals(instance.getArchivesId()))
                    .findFirst()
                    .orElse(null);

            if (parentInstance == null) {
                continue;
            }

            boolean parentNeedUpdate = false;

            // 计算上传状态
            if (StringUtils.isNotBlank(uploadState)) {
                String newUploadState = calculateUploadState(children);
                if (!newUploadState.equals(parentInstance.getUploadState())) {
                    parentInstance.setUploadState(newUploadState);
                    parentNeedUpdate = true;
                }
            }

            // 计算审核状态
            if (StringUtils.isNotBlank(auditState)) {
                String newAuditState = calculateAuditState(children);
                if (!newAuditState.equals(parentInstance.getAuditState())) {
                    parentInstance.setAuditState(newAuditState);
                    parentNeedUpdate = true;
                }
            }

            if (parentNeedUpdate) {
                toUpdate.add(parentInstance);
            }
        }

        // 6. 批量更新所有需要更新的父节点
        if (!toUpdate.isEmpty()) {
            // 使用批量更新提升性能
            batchUpdateInstances(toUpdate);
        }
    }

    /**
     * 构建从当前节点到根节点的父节点路径
     */
    private List<String> buildParentPath(DgdocArchivesInstance currentInstance, String projectMonomerId, String archivesType) {
        List<String> parentPath = new ArrayList<>();
        String currentParentId = currentInstance.getParentId();
        
        // 限制递归深度，避免无限循环
        int maxDepth = 10;
        int depth = 0;
        
        while (StringUtils.isNotBlank(currentParentId) && depth < maxDepth) {
            parentPath.add(currentParentId);
            
            // 查询父节点
            LambdaQueryWrapper<DgdocArchivesInstance> parentQuery = Wrappers.lambdaQuery();
            parentQuery.eq(DgdocArchivesInstance::getArchivesId, currentParentId);
            parentQuery.eq(DgdocArchivesInstance::getProjectMonomerId, projectMonomerId);
            parentQuery.eq(DgdocArchivesInstance::getArchivesType, archivesType);
            
            DgdocArchivesInstance parentInstance = archivesInstanceMapper.selectOne(parentQuery);
            if (parentInstance == null) {
                break;
            }
            
            currentParentId = parentInstance.getParentId();
            depth++;
        }
        
        return parentPath;
    }

    /**
     * 计算上传状态
     */
    private String calculateUploadState(List<DgdocArchivesInstance> children) {
        if (children.stream().allMatch(p -> UploadState.UPLOADED.getCode().equals(p.getUploadState()))) {
            return UploadState.UPLOADED.getCode();
        } else if (children.stream().allMatch(p -> UploadState.UNUPLOAD.getCode().equals(p.getUploadState()))) {
            return UploadState.UNUPLOAD.getCode();
        } else {
            return UploadState.PARTUPLOAD.getCode();
        }
    }

    /**
     * 计算审核状态
     */
    private String calculateAuditState(List<DgdocArchivesInstance> children) {
        if (children.stream().allMatch(p -> AuditState.PASS.getCode().equals(p.getAuditState()))) {
            return AuditState.PASS.getCode();
        } else if (children.stream().allMatch(p -> AuditState.UNAUDITED.getCode().equals(p.getAuditState()))) {
            return AuditState.UNAUDITED.getCode();
        } else {
            return AuditState.PARTAUDITED.getCode();
        }
    }

    /**
     * 批量更新档案实例
     */
    private void batchUpdateInstances(List<DgdocArchivesInstance> instances) {
        if (instances.isEmpty()) {
            return;
        }

        // 分批处理，避免单次更新过多数据
        int batchSize = 50;
        for (int i = 0; i < instances.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, instances.size());
            List<DgdocArchivesInstance> batch = instances.subList(i, endIndex);

            for (DgdocArchivesInstance instance : batch) {
                archivesInstanceMapper.updateById(instance);
            }
        }

        log.info("批量更新档案实例完成，数量: {}", instances.size());
    }

    /**
     * 状态更新请求DTO
     */
    public static class StateUpdateRequest {
        private String archivesId;
        private String projectMonomerId;
        private String archivesType;
        private String uploadState;
        private String auditState;

        // 构造函数
        public StateUpdateRequest(String archivesId, String projectMonomerId, String archivesType, 
                                String uploadState, String auditState) {
            this.archivesId = archivesId;
            this.projectMonomerId = projectMonomerId;
            this.archivesType = archivesType;
            this.uploadState = uploadState;
            this.auditState = auditState;
        }

        // Getters
        public String getArchivesId() { return archivesId; }
        public String getProjectMonomerId() { return projectMonomerId; }
        public String getArchivesType() { return archivesType; }
        public String getUploadState() { return uploadState; }
        public String getAuditState() { return auditState; }
    }
}

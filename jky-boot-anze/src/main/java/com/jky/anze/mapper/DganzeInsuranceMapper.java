package com.jky.anze.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.anze.domain.DganzeInsurance;
import com.jky.anze.domain.vo.DgPcProjectItemVo;
import org.apache.ibatis.annotations.Param;

/**
 * @description:
 * @author: cq
 * @date: 2025/4/22 17:52
 */

public interface DganzeInsuranceMapper extends BaseMapper<DganzeInsurance> {

    IPage<DgPcProjectItemVo> queryPcProjectPage(Page<Object> objectPage, @Param(Constants.WRAPPER) QueryWrapper<DgPcProjectItemVo> queryWrapper);

    IPage<DgPcProjectItemVo> selectConsSiteWithOrg(Page<Object> objectPage, @Param(Constants.WRAPPER) QueryWrapper<DgPcProjectItemVo> queryWrapper);

    IPage<DgPcProjectItemVo> selectConsSiteWithInsurance(Page<Object> objectPage, @Param(Constants.WRAPPER) QueryWrapper<DgPcProjectItemVo> queryWrapper);
}


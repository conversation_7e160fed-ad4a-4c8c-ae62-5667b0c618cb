<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jky.anze.mapper.DganzeInsuranceMapper">

    <select id="queryPcProjectPage" resultType="com.jky.anze.domain.vo.DgPcProjectItemVo">
        SELECT
        s.report_status as reportType,
        s.has_insurance_order as hasInsuranceOrder,
        s.has_org as hasOrg,
        m.cons_site_name as consSiteName,
        n.project_name as projectName,
        m.updatetime as updateTime,
        m.id as id,
        m.project_id as projectId,
        m.agreement_price as agreementPrice,
        m.cons_size as consSize,
        ins.is_low_contract_amount as isLowContractAmount
        from dgdoc_aqjd_cons_site m
        left join dgdoc_project n on m.project_id = n.project_id
        left join dganze_insurance ins on m.id = ins.project_id
        left join dganze_insurance_state_maintain s on m.id = s.project_id
        ${ew.customSqlSegment}
    </select>

    <select id="selectConsSiteWithOrg" resultType="com.jky.anze.domain.vo.DgPcProjectItemVo">
        SELECT
        s.report_status            as reportType,
        s.has_insurance_order      as hasInsuranceOrder,
        s.has_org                  as hasOrg,
        m.cons_site_name           as consSiteName,
        n.project_name             as projectName,
        m.updatetime               as updateTime,
        m.id                       as id,
        m.project_id               as projectId,
        m.agreement_price          as agreementPrice,
        m.cons_size                as consSize,
        ins.is_low_contract_amount as isLowContractAmount
        FROM
        dganze_insurance_org org
        JOIN
        dgdoc_aqjd_cons_site m ON org.project_id = m.id
        LEFT JOIN dganze_insurance_service_record sr ON sr.id = (
        SELECT id
        FROM dganze_insurance_service_record
        WHERE project_id = m.project_id
        ORDER BY service_time DESC
        LIMIT 1
        )
        LEFT JOIN dgdoc_project n ON m.project_id = n.project_id
        left join dganze_insurance ins on m.id = ins.project_id
        left join dganze_insurance_state_maintain s on m.id = s.project_id
        ${ew.customSqlSegment}
    </select>

    <select id="selectConsSiteWithInsurance" resultType="com.jky.anze.domain.vo.DgPcProjectItemVo">
        SELECT
        s.report_status as reportType,
        s.has_insurance_order as hasInsuranceOrder,
        s.has_org as hasOrg,
        m.cons_site_name as consSiteName,
        n.project_name as projectName,
        m.updatetime as updateTime,
        m.id as id,
        m.project_id as projectId,
        m.agreement_price as agreementPrice,
        m.cons_size as consSize,
        ins.is_low_contract_amount as isLowContractAmount
        FROM dgdoc_aqjd_cons_site m
        LEFT JOIN dganze_insurance ins
        ON m.id = ins.project_id
        LEFT JOIN dgdoc_project n ON m.project_id = n.project_id
        left join dganze_insurance_state_maintain s on m.id = s.project_id
        ${ew.customSqlSegment}
    </select>
</mapper>
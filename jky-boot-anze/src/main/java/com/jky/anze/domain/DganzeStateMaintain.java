package com.jky.anze.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("dganze_insurance_state_maintain")
public class DganzeStateMaintain {
    @TableId
    private Long id; // 主键ID

    private String projectId; // 项目ID

    private Integer reportStatus; // 报告状态：0:未上传；1：已上传

    private Integer hasInsuranceOrder; // 是否上传保单：0:未上传保单；1：已上传保单

    private Integer hasOrg; // 是否选择机构：0:未选择机构；1：已上传机构

    private Date createTime; // 创建时间

    private Date updateTime; // 更新时间
}
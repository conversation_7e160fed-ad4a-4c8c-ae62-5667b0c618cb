package com.jky.anze.domain.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:
 * @author: cq
 * @date: 2025/4/30 16:55
 */
@Data
public class DgdocPcProjectBo {


    @ApiModelProperty("项目名称")
    private String consSiteName;

    @ApiModelProperty("所属项目")
    private String projectName;

    @ApiModelProperty("审核状态")
    private Integer checkType;

    @ApiModelProperty("服务报告状态")
    private Integer reportType;

    @ApiModelProperty("保单上传状态")
    private Integer hasInsuranceOrder;

    @ApiModelProperty("机构关联状态")
    private Integer hasOrg;

    @ApiModelProperty("保险公司关联项目 0-全部项目 1-关联项目")
    private Integer insuranceProject;
}


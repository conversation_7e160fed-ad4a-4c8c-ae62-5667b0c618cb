package com.jky.anze.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 机构和专家关联表实体类
 */
@Data
@TableName("dganze_org_expert")
public class DganzeOrgExpert {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 机构ID
     */
    private String orgId;

    /**
     * 专家ID
     */
    private String expertId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;
}
package com.jky.anze.util;

import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;

/**
 * @description:
 * @author: cq
 * @date: 2025/4/27 10:34
 */

public class LoginUserHelper {

    public static String GetUserType() {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        return user.getEntType();
    }
    public static String GetUserName() {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        return user.getUsername();
    }

    public static String GetOrgId() {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        return user.getOrganizationId();
    }
}


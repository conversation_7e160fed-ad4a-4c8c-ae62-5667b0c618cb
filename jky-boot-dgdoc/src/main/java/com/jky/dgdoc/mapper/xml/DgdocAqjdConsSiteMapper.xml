<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jky.dgdoc.mapper.DgdocAqjdConsSiteMapper">

    <select id="selectAllConsSiteWithInsurance" resultType="com.jky.dgdoc.domain.vo.DgdocAqjdConsSiteVo">
        SELECT
        site.*,
        ins.insurance_id,
        ins.is_low_contract_amount
        FROM dgdoc_aqjd_cons_site site
        LEFT JOIN dganze_insurance ins
        ON site.id = ins.project_id
        WHERE
        site.project_status = 0
        <if test="orgId != null and orgId != ''">
            AND (ins.org_id = #{orgId} OR ins.insurance_id IS NULL)
        </if>
        <if test="orgId == null or orgId == ''">
            AND 1=1
        </if>
        <if test="consSiteName != null and consSiteName != ''">
            AND site.cons_site_name LIKE CONCAT('%', #{consSiteName}, '%')
        </if>
        <if test="developOrgName != null and developOrgName != ''">
            AND site.develop_org_name LIKE CONCAT('%', #{developOrgName}, '%')
        </if>
        ORDER BY site.updatetime DESC
    </select>

    <select id="selectConsSiteWithOrg" resultType="com.jky.dgdoc.domain.vo.DgdocAqjdConsSiteVo">
        SELECT
        site.id,
        site.cons_site_name,
        site.cons_site_addr,
        site.township_name,
        site.updatetime,
        sr.audit_state
        FROM
        dganze_insurance_org org
        JOIN
        dgdoc_aqjd_cons_site site ON org.project_id = site.id
        LEFT JOIN dganze_insurance_service_record sr ON sr.id = (
        SELECT id
        FROM dganze_insurance_service_record
        WHERE project_id = site.project_id
        ORDER BY service_time DESC
        LIMIT 1
        )
        <where>
            site.project_status = 0
            <if test="orgId != null and orgId != ''">
                AND org.org_id = #{orgId}
            </if>
            <if test="consSiteName != null and consSiteName != ''">
                AND site.cons_site_name LIKE CONCAT('%', #{consSiteName}, '%')
            </if>
            <if test="developOrgName != null and developOrgName != ''">
                AND site.develop_org_name LIKE CONCAT('%', #{developOrgName}, '%')
            </if>
        </where>
        ORDER BY org.create_time DESC, site.updatetime DESC
    </select>

    <select id="selectRelatedConsSiteWithInsurance" resultType="com.jky.dgdoc.domain.vo.DgdocAqjdConsSiteVo">
        SELECT
        site.*,
        ins.insurance_id,
        ins.is_low_contract_amount
        FROM dgdoc_aqjd_cons_site site
        LEFT JOIN dganze_insurance ins
        ON site.id = ins.project_id
        WHERE
        site.project_status = 0
        <if test="orgId != null and orgId != ''">
            AND ins.org_id = #{orgId}
        </if>
        <if test="orgId == null or orgId == ''">
            AND 1=1
        </if>
        <if test="consSiteName != null and consSiteName != ''">
            AND site.cons_site_name LIKE CONCAT('%', #{consSiteName}, '%')
        </if>
        <if test="developOrgName != null and developOrgName != ''">
            AND site.develop_org_name LIKE CONCAT('%', #{developOrgName}, '%')
        </if>
        ORDER BY site.updatetime DESC
    </select>

</mapper>
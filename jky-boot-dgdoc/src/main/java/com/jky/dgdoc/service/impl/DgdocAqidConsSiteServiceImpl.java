package com.jky.dgdoc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jky.dgdoc.constants.UserRoleConstants;
import com.jky.dgdoc.domain.DgdocAqjdConsSite;
import com.jky.dgdoc.domain.PdProjectCollect;
import com.jky.dgdoc.domain.query.PdProjectCollectQuery;
import com.jky.dgdoc.domain.vo.DgdocAqjdConsSiteVo;
import com.jky.dgdoc.domain.vo.PdProjectCollectTableVo;
import com.jky.dgdoc.mapper.DgdocAqjdConsSiteMapper;
import com.jky.dgdoc.mapper.DgdocProjectMapper;
import com.jky.dgdoc.service.IDgdocAqjdConsSiteService;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 档案接口
 */
@Service
public class DgdocAqidConsSiteServiceImpl
        extends ServiceImpl<DgdocAqjdConsSiteMapper, DgdocAqjdConsSite> implements IDgdocAqjdConsSiteService {
    @Autowired
    DgdocProjectMapper dgdocProjectMapper;

    @Override
    public IPage<DgdocAqjdConsSiteVo> queryList(DgdocAqjdConsSiteVo query, Integer pageNo, Integer pageSize) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Set<String> userRolesSet = dgdocProjectMapper.getUserRolesSet(sysUser.getUsername());
        List<String> targetRoles = Arrays.asList(UserRoleConstants.ADMIN, UserRoleConstants.DGDOC_CHECKER, UserRoleConstants.DGDOC_COMMON);
        String userOrganizationId = null;

        IPage<DgdocAqjdConsSite> pageList;
        IPage<PdProjectCollect> projectCollectIPage;
        //如果用户角色不包含管理员，则查询本部门数据以及关联参建单位数据
        if (Collections.disjoint(userRolesSet, targetRoles)) {
           /* List<String> deptIds = baseMapper.getUserDepId(sysUser.getId());
            if (CollectionUtil.isEmpty(deptIds)) {
                return new Page<>();
            }
            Iterator<String> iterator = deptIds.iterator();
            while (iterator.hasNext()) {
                String deptId = iterator.next();
                if (iterator.hasNext()) {
                    lqw.eq(PdProjectCollect::getDeptId, deptId).or();
                } else {
                    lqw.eq(PdProjectCollect::getDeptId, deptId);
                }
            }*/

            userOrganizationId = dgdocProjectMapper.getUserOrganizationId(sysUser.getId());

            if (StringUtils.isBlank(userOrganizationId)) {
                return new Page<>();
            }

            projectCollectIPage = dgdocProjectMapper.pageList(new Page<>(1, 999), new PdProjectCollectQuery(), userOrganizationId);
            if (projectCollectIPage.getTotal() <=0) {
                return new Page<>();
            }
            List<String>projectList = projectCollectIPage.getRecords().stream().map(PdProjectCollect::getProjectId).collect(Collectors.toList());

            QueryWrapper<DgdocAqjdConsSite> qw = new QueryWrapper<>();
            qw.like(StringUtils.isNotBlank(query.getConsSiteName()), "cons_site_name", query.getConsSiteName());
            qw.eq(StringUtils.isNotBlank(query.getProjectStatus()), "project_status", query.getProjectStatus());
            qw.in("project_id",projectList);
            qw.orderByDesc("updatetime");
            pageList = baseMapper.selectPage(new Page<>(pageNo, pageSize), qw);
        } else {
            LambdaQueryWrapper<DgdocAqjdConsSite> lqw = new LambdaQueryWrapper<>();
            lqw.like(StringUtils.isNotBlank(query.getConsSiteName()), DgdocAqjdConsSite::getConsSiteName, query.getConsSiteName());
            lqw.eq(StringUtils.isNotBlank(query.getProjectStatus()), DgdocAqjdConsSite::getProjectStatus, query.getProjectStatus());
            lqw.orderByDesc(DgdocAqjdConsSite::getUpdatetime);
            pageList = baseMapper.selectPage(new Page<>(pageNo, pageSize), lqw);
        }
        for (DgdocAqjdConsSite item :pageList.getRecords()){
            PdProjectCollect projectCollect = dgdocProjectMapper.selectById(item.getProjectId());
            if(ObjectUtil.isNotEmpty(projectCollect)){
                item.setProjectName(projectCollect.getProjectName());
            }
        }
        return pageList.convert(e -> BeanUtil.toBean(e, DgdocAqjdConsSiteVo.class));
    }
}

package com.jky.dgdoc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.dgdoc.domain.DgdocAqjdConsSite;
import com.jky.dgdoc.domain.vo.DgdocAqjdConsSiteVo;
import org.apache.ibatis.annotations.Param;

public interface DgdocAqjdConsSiteMapper extends BaseMapper<DgdocAqjdConsSite> {
    IPage<DgdocAqjdConsSiteVo> selectAllConsSiteWithInsurance(
            @Param("page") Page<DgdocAqjdConsSiteVo> page,
            @Param("orgId") String orgId,
            @Param("consSiteName") String consSiteName,
            @Param("developOrgName") String developOrgName
    );

    IPage<DgdocAqjdConsSiteVo> selectConsSiteWithOrg(
            @Param("page") Page<DgdocAqjdConsSiteVo> page,
            @Param("orgId") String orgId,
            @Param("consSiteName") String consSiteName,
            @Param("developOrgName") String developOrgName
    );

    IPage<DgdocAqjdConsSiteVo> selectRelatedConsSiteWithInsurance(
            @Param("page") Page<DgdocAqjdConsSiteVo> page,
            @Param("orgId") String orgId,
            @Param("consSiteName") String consSiteName,
            @Param("developOrgName") String developOrgName
    );
}

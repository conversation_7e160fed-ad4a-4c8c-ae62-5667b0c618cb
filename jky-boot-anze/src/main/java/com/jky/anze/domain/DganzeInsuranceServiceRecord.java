package com.jky.anze.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.sql.Timestamp;

@Data
@TableName("dganze_insurance_service_record")
public class DganzeInsuranceServiceRecord {
    @TableId
    private String id; // 主键ID

    private String projectId; // 项目ID

    private String projectName;
    private String orgId; // 服务机构ID

    private int serviceType; // 服务类型

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Timestamp serviceTime; // 服务时间

    private String entryExitPhoto; // 出入场照片（存储图片路径）

    private String scenePhoto; // 现场照片（存储图片路径）

    private String location; // 定位信息

    private String servicePersonnel; // 服务人员

    private String personnelQualification; // 人员资质（存储图片路径）

    private String serviceResult; // 服务结果（存储图片路径）

    private int auditState; // 审核状态（0：审核中，1：审核通过，2：驳回）

    private String createBy; // 创建者

    private String updateBy; // 更新者

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp createdTime; // 创建时间

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp updatedTime; // 更新时间
}
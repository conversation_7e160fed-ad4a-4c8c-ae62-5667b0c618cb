<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.jky.boot</groupId>
  <artifactId>jky-boot-parent</artifactId>
  <version>3.1</version>
  <packaging>pom</packaging>
  
  	<parent>
	    <groupId>org.springframework.boot</groupId>
	    <artifactId>spring-boot-starter-parent</artifactId>
	    <version>2.7.18</version>
	    <relativePath/>
	</parent>

	<properties>
		<jkyboot.version>3.1</jkyboot.version>
		<java.version>1.8</java.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <spring-cloud.version>2021.0.3</spring-cloud.version>
        <spring-cloud-alibaba.version>2021.1</spring-cloud-alibaba.version>
		<xxl-job-core.version>2.2.0</xxl-job-core.version>
        <fastjson.version>2.0.18</fastjson.version>
		<knife4j-spring-boot-starter.version>3.0.3</knife4j-spring-boot-starter.version>
		<knife4j-spring-ui.version>2.0.9</knife4j-spring-ui.version>
		<!-- 数据库驱动 -->
		<postgresql.version>42.3.8</postgresql.version>
		<ojdbc6.version>11.2.0.3</ojdbc6.version>
		<sqljdbc4.version>4.0</sqljdbc4.version>
		<mysql-connector-java.version>8.0.27</mysql-connector-java.version>
		<!-- 动态数据源-->
		<dynamic-datasource-spring-boot-starter.version>3.2.0</dynamic-datasource-spring-boot-starter.version>
		<hutool.version>5.8.22</hutool.version>
		<redisson.version>3.16.1</redisson.version>
		<commons-beanutils.version>1.9.4</commons-beanutils.version>
		<guava.version>32.0.0-jre</guava.version>
		<mybatis-plus.version>3.5.3.2</mybatis-plus.version>
		<jsqlparser.version>4.6</jsqlparser.version>
		<druid.version>1.2.4</druid.version>
		<commons.version>2.11.0</commons.version>
		<aliyun-java-sdk-dysmsapi.version>2.1.0</aliyun-java-sdk-dysmsapi.version>
		<aliyun.oss.version>3.11.2</aliyun.oss.version>
		<shiro.version>1.13.0</shiro.version>
		<java-jwt.version>3.11.0</java-jwt.version>
		<shiro-redis.version>3.1.0</shiro-redis.version>
		<codegenerate.version>1.4.4</codegenerate.version>
		<!-- 支持poi5.0.0-->
		<autopoi-web.version>1.0.1</autopoi-web.version> 
		<minio.version>8.0.3</minio.version>
		<justauth-spring-boot-starter.version>1.3.4</justauth-spring-boot-starter.version>
		<dom4j.version>1.6.1</dom4j.version>
		<qiniu-java-sdk.version>7.4.0</qiniu-java-sdk.version>
		<!-- Log4j2惊爆0Day漏洞-->
		<log4j2.version>2.17.1</log4j2.version>
		<logback.version>1.2.9</logback.version>
		<flowable.version>6.7.2</flowable.version>
	</properties>

	<modules>
        <module>jky-boot-base</module>
<!--        <module>jky-boot-module-demo</module>-->
        <module>jky-boot-module-system</module>
        <module>jky-module-estar</module>
        <module>jky-boot-module-flowable</module>
	    <module>jky-boot-module-im</module>
	    <module>jky-boot-module-erp</module>
        <module>jky-boot-dgdoc</module>
		<module>jky-boot-anze</module>
        <!--需要微服务，请打开
        <module>jky-boot-starter</module>
        <module>jky-cloud-module</module>-->
		
  </modules>

	<distributionManagement>
	  	<repository>
            <id>jeecg</id>
            <name>jeecg Repository</name>
            <url>http://maven.jeecg.com:8090/nexus/content/repositories/jky</url>
        </repository>
        <snapshotRepository>
	        <id>jeecg-snapshots</id>
            <name>jeecg Snapshot Repository</name>
            <url>http://maven.jeecg.com:8090/nexus/content/repositories/snapshots/</url>
	    </snapshotRepository>
	</distributionManagement>
	<repositories>
		<repository>
            <id>aliyun</id>
            <name>aliyun Repository</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <snapshots>
				<enabled>false</enabled>
			</snapshots>
        </repository>
		<repository>
            <id>jeecg</id>
            <name>jeecg Repository</name>
            <url>https://maven.jeecg.org/nexus/content/repositories/jky</url>
            <snapshots>
				<enabled>false</enabled>
			</snapshots>
        </repository>
	</repositories>

	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<!-- Lombok -->
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>
		<!-- json -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>${fastjson.version}</version>
		</dependency>
	</dependencies>

	<dependencyManagement>
		<dependencies>
			<!-- spring-cloud-->
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<!-- spring-cloud-alibaba -->
			<dependency>
				<groupId>com.alibaba.cloud</groupId>
				<artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>

			<!-- system 模块-->
			<dependency>
				<groupId>com.jky.boot</groupId>
				<artifactId>jky-boot-module-system</artifactId>
				<version>${jkyboot.version}</version>
			</dependency>

			<!-- jky tools -->
			<dependency>
				<groupId>com.jky.boot</groupId>
				<artifactId>jky-boot-base-tools</artifactId>
				<version>${jkyboot.version}</version>
			</dependency>
			<!-- jky core -->
			<dependency>
				<groupId>com.jky.boot</groupId>
				<artifactId>jky-boot-base-core</artifactId>
				<version>${jkyboot.version}</version>
			</dependency>
			<!-- system 单体 api -->
			<dependency>
				<groupId>com.jky.boot</groupId>
				<artifactId>jky-system-local-api</artifactId>
				<version>${jkyboot.version}</version>
			</dependency>

			<!--xxl-job定时任务-->
			<dependency>
				<groupId>com.jky.boot</groupId>
				<artifactId>jky-boot-starter-job</artifactId>
				<version>${jkyboot.version}</version>
			</dependency>

			<!--redis分布式锁-->
			<dependency>
				<groupId>com.jky.boot</groupId>
				<artifactId>jky-boot-starter-lock</artifactId>
				<version>${jkyboot.version}</version>
			</dependency>

			<!--rabbitmq消息队列-->
			<dependency>
				<groupId>com.jky.boot</groupId>
				<artifactId>jky-boot-starter-rabbitmq</artifactId>
				<version>${jkyboot.version}</version>
			</dependency>


			<!-- 七牛云SDK -->
			<dependency>
				<groupId>com.qiniu</groupId>
				<artifactId>qiniu-java-sdk</artifactId>
				<version>${qiniu-java-sdk.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>okhttp</artifactId>
						<groupId>com.squareup.okhttp3</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<!-- dom4j -->
			<dependency>
				<groupId>dom4j</groupId>
				<artifactId>dom4j</artifactId>
				<version>${dom4j.version}</version>
			</dependency>
			<!-- redisson -->
			<dependency>
				<groupId>org.redisson</groupId>
				<artifactId>redisson</artifactId>
				<version>${redisson.version}</version>
			</dependency>

			<!-- guava工具类 -->
			<dependency>
				<groupId>com.google.guava</groupId>
				<artifactId>guava</artifactId>
				<version>${guava.version}</version>
			</dependency>

			<!-- hutool工具类-->
			<dependency>
				<groupId>cn.hutool</groupId>
				<artifactId>hutool-core</artifactId>
				<version>${hutool.version}</version>
			</dependency>
			<dependency>
				<groupId>cn.hutool</groupId>
				<artifactId>hutool-crypto</artifactId>
				<version>${hutool.version}</version>
			</dependency>

			<!-- commons-beanutils -->
			<dependency>
				<groupId>commons-beanutils</groupId>
				<artifactId>commons-beanutils</artifactId>
				<version>${commons-beanutils.version}</version>
			</dependency>

			<dependency>
				<groupId>commons-fileupload</groupId>
				<artifactId>commons-fileupload</artifactId>
				<version>1.4</version>
				<exclusions>
					<exclusion>
						<artifactId>commons-io</artifactId>
						<groupId>commons-io</groupId>
					</exclusion>
				</exclusions>
			</dependency>

			<!-- justauth第三方登录  -->
			<dependency>
				<groupId>com.xkcoding.justauth</groupId>
				<artifactId>justauth-spring-boot-starter</artifactId>
				<version>${justauth-spring-boot-starter.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>hutool-core</artifactId>
						<groupId>cn.hutool</groupId>
					</exclusion>
					<exclusion>
						<artifactId>fastjson</artifactId>
						<groupId>com.alibaba</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.squareup.okhttp3</groupId>
				<artifactId>okhttp</artifactId>
				<version>4.4.1</version>
			</dependency>

			<dependency>
				<groupId>io.minio</groupId>
				<artifactId>minio</artifactId>
				<version>${minio.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>okio</artifactId>
						<groupId>com.squareup.okio</groupId>
					</exclusion>
					<exclusion>
						<artifactId>okhttp</artifactId>
						<groupId>com.squareup.okhttp3</groupId>
					</exclusion>
				</exclusions>
			</dependency>		
		</dependencies>
	</dependencyManagement>

	<build>
		<plugins>
			<!--<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
			 指定JDK编译版本 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
             <!-- 打包跳过测试 -->
            <plugin>
		        <groupId>org.apache.maven.plugins</groupId>
		        <artifactId>maven-surefire-plugin</artifactId>
		        <configuration>
		          <skipTests>true</skipTests>
		        </configuration>
	        </plugin>
	         <!-- 避免font文件的二进制文件格式压缩破坏 -->
	         <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>woff</nonFilteredFileExtension>
                        <nonFilteredFileExtension>woff2</nonFilteredFileExtension>
                        <nonFilteredFileExtension>eot</nonFilteredFileExtension>
                        <nonFilteredFileExtension>ttf</nonFilteredFileExtension>
                        <nonFilteredFileExtension>svg</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
		</plugins>
		<resources>
			<resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
			<resource>
				<directory>src/main/java</directory>
				<includes>
					<include>**/*.xml</include>
					<include>**/*.json</include>
					<include>**/*.ftl</include>
				</includes>
			</resource>
		</resources>
	</build>
     <!-- 环境 -->
    <profiles>
        <!-- 开发 -->
        <profile>
            <id>dev</id>
            <activation>
                <!--默认激活配置-->
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <!--当前环境-->
                <profile.name>dev</profile.name>
                <!--配置文件前缀-->
                <prefix.name>jky</prefix.name>
                <!--Nacos配置中心地址-->
                <config.server-addr>127.0.0.1:8848</config.server-addr>
                <!--Nacos配置中心命名空间,用于支持多环境.这里必须使用ID，不能使用名称,默认为空-->
                <config.namespace/>
                 <!--Nacos配置分组名称-->
                <config.group>DEFAULT_GROUP</config.group>
                <!--Nacos服务发现地址-->
                <discovery.server-addr>127.0.0.1:8848</discovery.server-addr>
            </properties>
        </profile>
        <!-- 测试 -->
        <profile>
            <id>test</id>
            <properties>
                <!--当前环境-->
                <profile.name>test</profile.name>
                <!--配置文件前缀-->
                <prefix.name>jky</prefix.name>
                <!--Nacos配置中心地址-->
                <config.server-addr>127.0.0.1:8848</config.server-addr>
                <!--Nacos配置中心命名空间,用于支持多环境.这里必须使用ID，不能使用名称,默认为空-->
                <config.namespace/>
               <!--Nacos配置分组名称-->
                <config.group>DEFAULT_GROUP</config.group>
                <!--Nacos服务发现地址-->
                <discovery.server-addr>127.0.0.1:8848</discovery.server-addr>
            </properties>
        </profile>
        <!-- 生产 -->
        <profile>
            <id>prod</id>
            <properties>
                <!--当前环境,生产环境为空-->
                <profile.name>prod</profile.name>
                <!--配置文件前缀-->
                <prefix.name>jky</prefix.name>
                <!--Nacos配置中心地址-->
                <config.server-addr>127.0.0.1:8848</config.server-addr>
                <!--Nacos配置中心命名空间,用于支持多环境.这里必须使用ID，不能使用名称,默认为空-->
                <config.namespace/>
                <!--Nacos配置分组名称-->
                <config.group>DEFAULT_GROUP</config.group>
                <!--Nacos服务发现地址-->
                <discovery.server-addr>127.0.0.1:8848</discovery.server-addr>
            </properties>
        </profile>
    </profiles>
</project>

/**
 *
 * (c) Copyright Ascensio System SIA 2021
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.jky.modules.estar.nd.office.documentserver.models.configurations;

import lombok.Getter;
import lombok.Setter;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

@Component
@Scope("prototype")
@Getter
@Setter
public class Info {  // the additional parameters for the document (document owner, folder where the document is stored, uploading date, sharing settings)
    private String owner = "Me";  // the name of the document owner/creator
    private Boolean favorite = null;  // the highlighting state of the Favorite icon
    private String uploaded = getDate();  // the document uploading date

    private String getDate() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("EEE MMM dd yyyy", Locale.US);
        return simpleDateFormat.format(new Date());
    }
}

package com.jky.anze.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.anze.constant.EntType;
import com.jky.anze.constant.InsuranceTypeConstant;
import com.jky.anze.domain.AnzeInsuranceEventRecord;
import com.jky.anze.domain.AnzeSysDepart;
import com.jky.anze.domain.AnzeSysUser;
import com.jky.anze.domain.DganzeExperts;
import com.jky.anze.domain.DganzeInsurance;
import com.jky.anze.domain.DganzeInsuranceFile;
import com.jky.anze.domain.DganzeInsuranceOrg;
import com.jky.anze.domain.DganzeInsuranceOrgServiceRecord;
import com.jky.anze.domain.DganzeInsuranceServiceRecord;
import com.jky.anze.domain.DganzeInsuranceWithFileUrl;
import com.jky.anze.domain.DganzeOrgExpert;
import com.jky.anze.domain.SysDict;
import com.jky.anze.domain.SysDictItem;
import com.jky.anze.domain.vo.DashboardStatsVo;
import com.jky.anze.domain.vo.DgdocAqjdConsSiteDetailVo;
import com.jky.anze.mapper.*;
import com.jky.anze.service.IAnzeInsuranceService;
import com.jky.anze.util.LoginUserHelper;
import com.jky.dgdoc.constants.UserRoleConstants;
import com.jky.dgdoc.domain.DgdocAqjdConsSite;
import com.jky.dgdoc.domain.vo.DgdocAqjdConsSiteVo;
import com.jky.dgdoc.mapper.DgdocAqjdConsSiteMapper;
import com.jky.dgdoc.mapper.DgdocProjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.MinioUtil;
import org.jeecg.modules.online.config.exception.BusinessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: cq
 * @date: 2025/4/22 17:57
 */

@Service
@Slf4j
public class AnzeInsuranceServiceImpl implements IAnzeInsuranceService {

    @Resource
    private DganzeInsuranceMapper insuranceMapper;
    @Resource
    private DganzeInsuranceFileMapper insuranceFileMapper;
    @Resource
    private DganzeInsuranceOrgMapper insuranceOrgMapper;
    @Resource
    private DganzeInsuranceServiceRecordMapper serviceRecordMapper;
    @Resource
    private AnzeSysDepartMapper anzeSysDepartMapper;
    @Resource
    private AnzeSysUserMapper anzeSysUserMapper;
    @Resource
    private DgdocAqjdConsSiteMapper dgdocAqjdConsSiteMapper;
    @Resource
    private DganzeExpertsMapper dganzeExpertsMapper;
    @Resource
    private DganzeOrgExpertMapper dganzeOrgExpertMapper;
    @Resource
    private DashboardStatsMapper dashboardStatsMapper;
    @Resource
    private AnzeSysDictMapper anzeSysDictMapper;
    @Resource
    private AnzeSysDictItemMapper anzeSysDictItemMapper;
    @Resource
    private DgdocProjectMapper dgdocProjectMapper;
    @Resource
    private AnzeInsuranceEventRecordMapper insuranceEventRecordMapper;
    @Resource
    private DganzeStateMaintainMapper dganzeStateMaintainMapper;

    // 水乡片区的镇街
    List<String> WATER_TOWNS = Arrays.asList("麻涌", "望牛墩", "道滘", "洪梅", "中堂");
    // 镇街的字典编码
    String TOWNSHIP_DICT_CODE = "dgzjjsp_project_town_id";

    private List<String> WATER_TOWNSHIP_CACHE = null; // 缓存结果

    private List<String> getWaterTownShipIds() {
        if (WATER_TOWNSHIP_CACHE != null) {
            return WATER_TOWNSHIP_CACHE; // 如果缓存已存在，直接返回
        }

        // 查询字典ID
        String dictId = anzeSysDictMapper.selectOne(new LambdaQueryWrapper<SysDict>()
                .eq(SysDict::getDictCode, TOWNSHIP_DICT_CODE)).getId();

        // 查询字典项并提取 item_value 列表
        List<SysDictItem> dictItems = anzeSysDictItemMapper.selectList(
                new LambdaQueryWrapper<SysDictItem>()
                        .eq(SysDictItem::getDictId, dictId)
        );

        // 模糊匹配 WATER_TOWNS 中的元素
        WATER_TOWNSHIP_CACHE = dictItems.stream()
                .filter(item -> WATER_TOWNS.stream().anyMatch(waterTown -> item.getItemText().contains(waterTown)))
                .map(SysDictItem::getItemValue)
                .collect(Collectors.toList());

        return WATER_TOWNSHIP_CACHE; // 返回缓存结果
    }

    @Override
    public void queryInsuranceInfo(List<DgdocAqjdConsSiteVo> records) {
        records.forEach(a -> {
            a.setHasEnsuranceOrder(
                    insuranceFileMapper.exists(new LambdaQueryWrapper<DganzeInsuranceFile>()
                            .eq(DganzeInsuranceFile::getProjectId, a.getProjectId())));
            a.setHasOrg(
                    insuranceOrgMapper.exists(new LambdaQueryWrapper<DganzeInsuranceOrg>()
                            .eq(DganzeInsuranceOrg::getProjectId, a.getProjectId()))
            );
        });
    }

    @Override
    public IPage<DgdocAqjdConsSiteVo> list(DgdocAqjdConsSiteVo query, Integer pageNo, Integer pageSize) {
        /**
         * 市级住建能看到全市数据，镇级住建能看到自己镇的数据（存在一对多特殊情况：水乡片区包含五个镇街，水乡片区能看到这五个镇的数据
         * 保险公司是能看到整体的未上传保单的工地列表，一个工地如果已经上传了保单就只能那个保险公司看到
         * 第三方机构看到哪些项目是根据保险公司给他们关联了哪些工地
         */
        IPage<DgdocAqjdConsSiteVo> page = null;
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        //AnzeSysUser sysUser = anzeSysUserMapper.selectOne(new LambdaQueryWrapper<AnzeSysUser>().eq(AnzeSysUser::getUsername, user.getUsername()));
        String entType = sysUser.getEntType();

        if (EntType.CITY.equals(entType)) {
            page = this.queryConsSites(query, pageNo, pageSize);
            //检测是否上传保单、服务机构是否选择
            this.queryInsuranceInfo(page.getRecords());

        } else if (EntType.TOWN.equals(entType)) {
            // 查询用户所属的镇街id,拼装到query中做分页查询
            QueryWrapper<AnzeSysDepart> departWrapper = new QueryWrapper<>();
            departWrapper.eq("organization_id", sysUser.getOrganizationId());
            AnzeSysDepart depart = anzeSysDepartMapper.selectOne(departWrapper);
            query.setTownshipId(depart.getTownShipId());
            page = this.queryConsSites(query, pageNo, pageSize);
            this.queryInsuranceInfo(page.getRecords());

        } else if (EntType.ORG.equals(entType)) {
            Page<DgdocAqjdConsSiteVo> p = new Page<>(pageNo, pageSize);
            page = dgdocAqjdConsSiteMapper.selectConsSiteWithOrg(p, sysUser.getOrganizationId(), query.getConsSiteName(), query.getDevelopOrgName());

        } else if (EntType.INSURANCE.equals(entType)) {
            Page<DgdocAqjdConsSiteVo> p = new Page<>(pageNo, pageSize);
            if (Objects.nonNull(query.getInsuranceProject()) && query.getInsuranceProject() == 1) {
                page = dgdocAqjdConsSiteMapper.selectRelatedConsSiteWithInsurance(p, sysUser.getOrganizationId(), query.getConsSiteName(), query.getDevelopOrgName());
            } else {
                if (StringUtils.isNotBlank(query.getConsSiteName()) || StringUtils.isNotBlank(query.getDevelopOrgName())) {
                    page = dgdocAqjdConsSiteMapper.selectAllConsSiteWithInsurance(p, sysUser.getOrganizationId(), query.getConsSiteName(), query.getDevelopOrgName());
                }
            }
            if (page != null) {
                page.getRecords().forEach(x -> {
                    x.setHasEnsuranceOrder(x.getInsuranceId() != null);
                });
                page.getRecords().forEach(x -> {
                    x.setHasOrg(
                            insuranceOrgMapper.exists(new LambdaQueryWrapper<DganzeInsuranceOrg>()
                                    .eq(DganzeInsuranceOrg::getProjectId, x.getId()))
                    );
                });
                page.getRecords().forEach(x -> {
                    x.setHasOrg(
                            insuranceOrgMapper.exists(new LambdaQueryWrapper<DganzeInsuranceOrg>()
                                    .eq(DganzeInsuranceOrg::getProjectId, x.getId()))
                    );
                });
            }
        }

        return page;
    }

    @Override
    public DgdocAqjdConsSiteDetailVo queryInsuranceDetailVo(DgdocAqjdConsSite pdProjectCollect) {
        DgdocAqjdConsSiteDetailVo vo = new DgdocAqjdConsSiteDetailVo();
        vo.setSite(pdProjectCollect);
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        AnzeSysUser sysUser = anzeSysUserMapper.selectOne(new LambdaQueryWrapper<AnzeSysUser>().eq(AnzeSysUser::getUsername, user.getUsername()));
        String entType = sysUser.getEntType();
        Set<String> userRolesSet = dgdocProjectMapper.getUserRolesSet(sysUser.getUsername());

        if (EntType.CITY.equals(entType) || EntType.TOWN.equals(entType) || userRolesSet.contains(UserRoleConstants.ADMIN)) {
            //监管部门详情
            DganzeInsurance dganzeInsurance = insuranceMapper.selectOne(new LambdaQueryWrapper<DganzeInsurance>()
                    .eq(DganzeInsurance::getProjectId, pdProjectCollect.getId()), false);
            vo.setInsurance(dganzeInsurance);

            // 查出项目关联服务机构的服务记录
            List<DganzeInsuranceOrg> orgs = insuranceOrgMapper.selectList(new LambdaQueryWrapper<DganzeInsuranceOrg>()
                    .eq(DganzeInsuranceOrg::getProjectId, pdProjectCollect.getId()));
            for (DganzeInsuranceOrg org : orgs) {
                List<DganzeInsuranceServiceRecord> records = serviceRecordMapper.selectList(new LambdaQueryWrapper<DganzeInsuranceServiceRecord>()
                        .eq(DganzeInsuranceServiceRecord::getProjectId, org.getProjectId())
                        .eq(DganzeInsuranceServiceRecord::getOrgId, org.getOrgId()));
                DganzeInsuranceOrgServiceRecord orgServiceRecord = new DganzeInsuranceOrgServiceRecord();
                orgServiceRecord.setOrg(org);
                orgServiceRecord.setRecord(records);
                vo.getOrgServiceRecords().add(orgServiceRecord);
            }

        } else if (EntType.INSURANCE.equals(entType)) {
            //保单详情，服务机构可能多个
            List<DganzeInsuranceOrg> dganzeInsuranceOrgs = insuranceOrgMapper.selectList(new LambdaQueryWrapper<DganzeInsuranceOrg>()
                    .eq(DganzeInsuranceOrg::getProjectId, pdProjectCollect.getId()));
            vo.setOrgs(dganzeInsuranceOrgs);
            for (DganzeInsuranceOrg org : dganzeInsuranceOrgs) {
                List<DganzeInsuranceServiceRecord> records = serviceRecordMapper.selectList(new LambdaQueryWrapper<DganzeInsuranceServiceRecord>()
                        .eq(DganzeInsuranceServiceRecord::getProjectId, org.getProjectId())
                        .eq(DganzeInsuranceServiceRecord::getOrgId, org.getOrgId()));
                DganzeInsuranceOrgServiceRecord orgServiceRecord = new DganzeInsuranceOrgServiceRecord();
                orgServiceRecord.setOrg(org);
                orgServiceRecord.setRecord(records);
                vo.getOrgServiceRecords().add(orgServiceRecord);
            }

            DganzeInsurance dganzeInsurance = insuranceMapper.selectOne(new LambdaQueryWrapper<DganzeInsurance>()
                    .eq(DganzeInsurance::getProjectId, pdProjectCollect.getId()), false);
            vo.setInsurance(dganzeInsurance);

            if (dganzeInsurance != null) {
                DganzeInsuranceFile dganzeInsuranceFile = insuranceFileMapper.selectOne(new LambdaQueryWrapper<DganzeInsuranceFile>()
                        .eq(DganzeInsuranceFile::getInsuranceId, dganzeInsurance.getInsuranceId()));
                vo.setInsuranceFile(dganzeInsuranceFile);
            }
        } else if (EntType.ORG.equals(entType)) {
            //服务机构详情，服务记录可能多条
            List<DganzeInsuranceServiceRecord> records = serviceRecordMapper.selectList(new LambdaQueryWrapper<DganzeInsuranceServiceRecord>()
                    .eq(DganzeInsuranceServiceRecord::getProjectId, pdProjectCollect.getId()));
            vo.setServiceRecords(records);
        }

        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean uploadInsuranceInfo(DganzeInsuranceWithFileUrl dganzeInsuranceWithFileUrl) {
        try {
            DgdocAqjdConsSite dgdocAqjdConsSite = dgdocAqjdConsSiteMapper.selectById(dganzeInsuranceWithFileUrl.getProjectId());
            // 合同价格和累计赔偿限额进行验证，累计赔偿限额不低于合同价格的20%
//            if (dgdocAqjdConsSite.getAgreementPrice() != null && dganzeInsuranceWithFileUrl.getMaxMoney() != null) {
//                BigDecimal agreementPrice = BigDecimal.valueOf(dgdocAqjdConsSite.getAgreementPrice()).multiply(BigDecimal.valueOf(10000));
//                BigDecimal maxMoney = BigDecimal.valueOf(dganzeInsuranceWithFileUrl.getMaxMoney());
//
//                // 计算20%的阈值
//                BigDecimal threshold = agreementPrice.multiply(BigDecimal.valueOf(0.2));
//
//                if (maxMoney.compareTo(threshold) < 0) {
//                    throw new IllegalArgumentException("累计赔偿限额不得低于合同价格的20%");
//                }
//            }

            DganzeInsurance dganzeInsurance = new DganzeInsurance();
            BeanUtil.copyProperties(dganzeInsuranceWithFileUrl, dganzeInsurance);
            dganzeInsurance.setOrgId(LoginUserHelper.GetOrgId());
            dganzeInsurance.setProjectName(dgdocAqjdConsSite.getConsSiteName());
            //合同金额小于等于工程造价的95%，则标记上
            if (dganzeInsurance.getContractAmount() != null && dgdocAqjdConsSite.getAgreementPrice() != null) {
                BigDecimal agreementPrice = BigDecimal.valueOf(dgdocAqjdConsSite.getAgreementPrice()).multiply(BigDecimal.valueOf(10000));
                BigDecimal contractAmount = BigDecimal.valueOf(dganzeInsurance.getContractAmount());
                if (contractAmount.compareTo(agreementPrice.multiply(BigDecimal.valueOf(0.95))) <= 0) {
                    dganzeInsurance.setIsLowContractAmount(1);
                }
            }
            insuranceMapper.insert(dganzeInsurance);

            DganzeInsuranceFile dganzeInsuranceFile = new DganzeInsuranceFile();
            dganzeInsuranceFile.setInsuranceId(dganzeInsurance.getInsuranceId());
            dganzeInsuranceFile.setProjectId(dganzeInsurance.getProjectId());
            dganzeInsuranceFile.setFilePath(dganzeInsuranceWithFileUrl.getFileUrl());
            dganzeInsuranceFile.setOrgId(dganzeInsurance.getOrgId());
            insuranceFileMapper.insert(dganzeInsuranceFile);


            AnzeInsuranceEventRecord r = new AnzeInsuranceEventRecord();
            r.setContentId(String.valueOf(dganzeInsuranceFile.getInsuranceId()));
            r.setProjectId(dgdocAqjdConsSite.getId());
            r.setCreateTime(new Date());
            r.setRecordTitle(dgdocAqjdConsSite.getConsSiteName());
            r.setRecordType(InsuranceTypeConstant.TYPE_ORDER_FILE_UPLOAD);
            r.setOrgId(LoginUserHelper.GetOrgId());
            r.setUserId(LoginUserHelper.GetUserName());

            insuranceEventRecordMapper.insert(r);

            // 更新工地表的保单状态
            dganzeStateMaintainMapper.upsertHasInsuranceOrder(dgdocAqjdConsSite.getId());
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            // 重新抛出异常以确保事务回滚
            throw new RuntimeException("上传保单信息失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean selectOrg(List<DganzeInsuranceOrg> dganzeInsuranceOrgList) {
        DgdocAqjdConsSite dgdocAqjdConsSite = dgdocAqjdConsSiteMapper.selectById(dganzeInsuranceOrgList.get(0).getProjectId());
        for (DganzeInsuranceOrg dganzeInsuranceOrg : dganzeInsuranceOrgList) {
            int result = insuranceOrgMapper.insert(dganzeInsuranceOrg);
            if (result <= 0) {
                throw new RuntimeException("服务机构插入失败");
            }
            AnzeInsuranceEventRecord r = new AnzeInsuranceEventRecord();
            r.setProjectId(dgdocAqjdConsSite.getId());
            r.setContentId(String.valueOf(dganzeInsuranceOrg.getInsuranceOrgId()));
            r.setCreateTime(new Date());
            r.setRecordTitle(dgdocAqjdConsSite.getConsSiteName());
            r.setRecordType(InsuranceTypeConstant.TYPE_ORDER_CHOOSE_ORG);
            r.setOrgId(LoginUserHelper.GetOrgId());
            r.setUserId(LoginUserHelper.GetUserName());
            int eventResult = insuranceEventRecordMapper.insert(r);
            if (eventResult <= 0) {
                throw new RuntimeException("事件记录插入失败");
            }

            // 更新工地表的服务机构状态
            dganzeStateMaintainMapper.upsertHasOrg(dgdocAqjdConsSite.getId());

        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean serviceRecord(DganzeInsuranceServiceRecord record) {
        try {
            //驳回重新填写
            if (record.getId() != null) {
                int result = serviceRecordMapper.updateById(record);
                return result > 0;
            }

            DgdocAqjdConsSite dgdocAqjdConsSite = dgdocAqjdConsSiteMapper.selectById(record.getProjectId());
            record.setProjectId(dgdocAqjdConsSite.getId());
            record.setProjectName(dgdocAqjdConsSite.getConsSiteName());
            record.setOrgId(LoginUserHelper.GetOrgId());
            int result = serviceRecordMapper.insert(record);
            record.setProjectName(dgdocAqjdConsSite.getConsSiteName());

            AnzeInsuranceEventRecord r = new AnzeInsuranceEventRecord();
            r.setContentId(String.valueOf(record.getId()));
            r.setProjectId(dgdocAqjdConsSite.getId());
            r.setCreateTime(new Date());
            r.setRecordTitle(dgdocAqjdConsSite.getConsSiteName());
            r.setRecordType(InsuranceTypeConstant.TYPE_SERVICE_RECORD);
            r.setOrgId(LoginUserHelper.GetOrgId());
            r.setUserId(LoginUserHelper.GetUserName());


            insuranceEventRecordMapper.insert(r);

            return result > 0;
        } catch (Exception e) {
            log.error("提交服务记录失败", e);
            throw new RuntimeException("提交服务记录失败", e);
        }
    }

    @Override
    public List<DganzeExperts> getExpertsByOrgId(String orgId) {
        // 先根据 orgId 查询出关联的专家 ID
        List<DganzeOrgExpert> relations = dganzeOrgExpertMapper.selectList(
                new LambdaQueryWrapper<DganzeOrgExpert>().eq(DganzeOrgExpert::getOrgId, orgId)
        );

        // 提取专家 ID
        List<String> expertIds = relations.stream()
                .map(DganzeOrgExpert::getExpertId)
                .collect(Collectors.toList());

        // 根据专家 ID 查询出所有专家信息
        return expertIds.size() > 0 ? dganzeExpertsMapper.selectList(
                new LambdaQueryWrapper<DganzeExperts>().in(DganzeExperts::getExid, expertIds)
        ) : new ArrayList<>();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addExpertRelation(String orgId, String expertId) {
        // 创建专家关联记录
        DganzeOrgExpert relation = new DganzeOrgExpert();
        relation.setOrgId(orgId);
        relation.setExpertId(expertId);
        int result = dganzeOrgExpertMapper.insert(relation);
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delExpertRelation(String orgId, String expertId) {
        // 删除专家关联记录
        int result = dganzeOrgExpertMapper.delete(new LambdaQueryWrapper<DganzeOrgExpert>()
                .eq(DganzeOrgExpert::getOrgId, orgId)
                .eq(DganzeOrgExpert::getExpertId, expertId));
        return result > 0;
    }

    @Override
    public DashboardStatsVo getDashboardStats(String townshipId, String startTime, String endTime) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String entType = sysUser.getEntType();
        String insuranceOrgId = null, serviceOrgId = null;
        List<String> townshipIds = null;
        if (StringUtils.isNotBlank(townshipId)) {
            townshipIds = Collections.singletonList(townshipId);
            if (WATER_TOWNS.contains(townshipId)) {
                townshipIds = WATER_TOWNS;
            }
        }
        SysDict dict = anzeSysDictMapper.selectOne(new QueryWrapper<SysDict>()
                .eq("dict_code", "dgzjjsp_project_town_id")
                .last("limit 1"));
        if (dict != null) {
            SysDictItem dictItem = anzeSysDictItemMapper.selectOne(new QueryWrapper<SysDictItem>()
                    .eq("dict_id", dict.getId())
                    .eq("item_value", townshipId)
                    .last("limit 1"));
            if (dictItem != null && dictItem.getItemText().contains("东莞")) {
                townshipIds = null;
            }
        }

        if (entType.equals(EntType.INSURANCE)) {
            insuranceOrgId = sysUser.getOrganizationId();
        } else if (entType.equals(EntType.ORG)) {
            serviceOrgId = sysUser.getOrganizationId();
        }

        DashboardStatsVo stats = new DashboardStatsVo();

        Integer totalProjects = dashboardStatsMapper.countTotalProjects(insuranceOrgId, serviceOrgId, townshipIds);
        Integer serviceProjects = dashboardStatsMapper.countServiceProjects(insuranceOrgId, serviceOrgId, townshipIds);
        Integer insuranceCnt = dashboardStatsMapper.countInsuranceCnt(insuranceOrgId, serviceOrgId, townshipIds, startTime, endTime);
        Double insuranceMoney = dashboardStatsMapper.sumInsuranceMoney(insuranceOrgId, serviceOrgId, townshipIds, startTime, endTime);
        Integer serviceTimes = dashboardStatsMapper.countServiceTimes(insuranceOrgId, serviceOrgId, townshipIds, startTime, endTime);
        Integer techConsultCount = dashboardStatsMapper.countServiceType(insuranceOrgId, serviceOrgId, townshipIds, 0, startTime, endTime);
        Integer emergencyDrillCount = dashboardStatsMapper.countServiceType(insuranceOrgId, serviceOrgId, townshipIds, 1, startTime, endTime);
        Integer safetyTrainingCount = dashboardStatsMapper.countServiceType(insuranceOrgId, serviceOrgId, townshipIds, 2, startTime, endTime);
        Integer siteInspectionCount = dashboardStatsMapper.countServiceType(insuranceOrgId, serviceOrgId, townshipIds, 3, startTime, endTime);
        Integer riskAssessmentCount = dashboardStatsMapper.countServiceType(insuranceOrgId, serviceOrgId, townshipIds, 4, startTime, endTime);
        Integer standardConstructionCount = dashboardStatsMapper.countServiceType(insuranceOrgId, serviceOrgId, townshipIds, 5, startTime, endTime);
        Integer otherServicesCount = dashboardStatsMapper.countServiceType(insuranceOrgId, serviceOrgId, townshipIds, 6, startTime, endTime);

        stats.setServiceProjects(serviceProjects);
        stats.setUnserviceProjects(totalProjects - serviceProjects);
        stats.setInsuranceCnt(insuranceCnt);
        stats.setInsuranceMoney(insuranceMoney == null ? 0.0 : insuranceMoney);
        stats.setServiceTimes(serviceTimes);
        stats.setTechConsultCount(techConsultCount);
        stats.setEmergencyDrillCount(emergencyDrillCount);
        stats.setSafetyTrainingCount(safetyTrainingCount);
        stats.setSiteInspectionCount(siteInspectionCount);
        stats.setRiskAssessmentCount(riskAssessmentCount);
        stats.setStandardConstructionCount(standardConstructionCount);
        stats.setOtherServicesCount(otherServicesCount);
        return stats;
    }

    @Override
    public List<DganzeExperts> getAvailableExperts() {
        return dganzeExpertsMapper.selectList(null);
    }

    @Override
    public void preview(String filePath, HttpServletResponse response) throws BusinessException, IOException {
        //从地址中获取inputStream
        if (filePath.startsWith("/" + MinioUtil.getBucketName())) {
            filePath = filePath.substring(1 + MinioUtil.getBucketName().length());
        }
        InputStream in = MinioUtil.getMinioFile(MinioUtil.getBucketName(), filePath
                .replace(MinioUtil.getMinioUrl(), ""));

        String fileExtension = this.getFileExtension(filePath);
        if (StringUtils.isEmpty(fileExtension)) {
            throw new BusinessException("文件类型异常");
        }

        responseFile(response, in, fileExtension);
    }

    private synchronized void responseFile(HttpServletResponse response, InputStream in, String fileType) throws IOException {
        if (in == null) {
            throw new JeecgBootException("获取文件流失败");
        }

        if ("jpg".equals(fileType) || "jpeg".equals(fileType) || "gif".equals(fileType) || "png".equals(fileType)) {
            response.setContentType("image/" + fileType);
        } else if ("pdf".equals(fileType)) {
            response.setContentType("application/pdf");
        } else {
            response.setContentType("multipart/form-data");
        }

        try (ServletOutputStream out = response.getOutputStream()) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = in.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
            }
            out.flush();
        } finally {
            in.close();
        }
    }

    private String getFileExtension(String fileName) {
        if (fileName == null) {
            return null; // 或返回空字符串根据需求
        }
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex != -1 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex + 1);
        } else {
            return "";
        }
    }

    @Override
    public IPage<DgdocAqjdConsSiteVo> queryConsSites(DgdocAqjdConsSiteVo query, Integer pageNo, Integer pageSize) {
        Page<DgdocAqjdConsSite> page = new Page<>(pageNo, pageSize);
        QueryWrapper<DgdocAqjdConsSite> qw = new QueryWrapper<>();
        qw.like(StringUtils.isNotBlank(query.getConsSiteName()), "cons_site_name", query.getConsSiteName());
        qw.like(StringUtils.isNotBlank(query.getDevelopOrgName()), "develop_org_name", query.getDevelopOrgName());
        // 如果 townshipId 不为空，则根据 townshipId 进行筛选
        if (StringUtils.isNotBlank(query.getTownshipId())) {
            if (this.getWaterTownShipIds().contains(query.getTownshipId())) {
                qw.in("township_id", WATER_TOWNSHIP_CACHE);
            } else {
                qw.eq("township_id", query.getTownshipId());
            }
        }
        qw.orderByDesc("updatetime");
        qw.eq("project_status", 0);
        IPage<DgdocAqjdConsSite> resultPage = dgdocAqjdConsSiteMapper.selectPage(page, qw);
        return resultPage.convert(e -> BeanUtil.toBean(e, DgdocAqjdConsSiteVo.class));
    }
}


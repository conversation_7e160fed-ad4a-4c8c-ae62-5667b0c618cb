package com.jky.anze.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 专家实体类
 */
@Data
@TableName("dgdoc_experts")
public class DganzeExperts {

    /**
     * 专家ID
     */
    private String exid;

    /**
     * 专家姓名
     */
    private String exname;

    /**
     * 专家身份证号
     */
    private String exidcard;

    /**
     * 专家所在城市
     */
    private String excity;

    /**
     * 专家联系电话
     */
    private String exmobileno;

    /**
     * 专家专业领域
     */
    private String exspecialty;

    /**
     * 专家评审类型
     */
    private String exreviewtype;

    /**
     * 专家所在部门
     */
    private String exdepartment;

    /**
     * 专家职务
     */
    private String exduty;

    /**
     * 专家部门地址
     */
    private String exdepaddress;

    /**
     * 专家邮箱
     */
    private String exemail;

    /**
     * 专家邮寄地址
     */
    private String exmail;

    /**
     * 备用字段
     */
    private String exbackup;

    /**
     * 状态
     */
    private Integer state;

    /**
     * 同步时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date jkSyncTime;
}

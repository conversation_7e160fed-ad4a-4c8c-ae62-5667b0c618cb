-- 数据库索引优化建议
-- 针对 dgdoc_archives_instance 表的性能优化

-- 1. 复合索引：项目单体ID + 档案类型 + 状态
-- 这个索引可以大幅提升按项目和档案类型查询的性能
CREATE INDEX idx_dgdoc_archives_instance_project_type_status 
ON dgdoc_archives_instance (project_monomer_id, archives_type, status);

-- 2. 复合索引：父级ID + 项目单体ID + 档案类型 + 状态
-- 这个索引专门优化 computeParentState 方法中的查询
CREATE INDEX idx_dgdoc_archives_instance_parent_project_type_status 
ON dgdoc_archives_instance (parent_id, project_monomer_id, archives_type, status);

-- 3. 复合索引：档案ID + 项目单体ID + 档案类型
-- 优化根据档案ID查询特定项目档案的场景
CREATE INDEX idx_dgdoc_archives_instance_archives_project_type 
ON dgdoc_archives_instance (archives_id, project_monomer_id, archives_type);

-- 4. 单独索引：上传状态
-- 如果经常按上传状态筛选，可以考虑添加此索引
CREATE INDEX idx_dgdoc_archives_instance_upload_state 
ON dgdoc_archives_instance (upload_state);

-- 5. 单独索引：审核状态
-- 如果经常按审核状态筛选，可以考虑添加此索引
CREATE INDEX idx_dgdoc_archives_instance_audit_state 
ON dgdoc_archives_instance (audit_state);

-- 6. 复合索引：档案类型 + 父级ID（用于树形结构查询）
-- 优化构建档案树结构时的查询性能
CREATE INDEX idx_dgdoc_archives_instance_type_parent 
ON dgdoc_archives_instance (archives_type, parent_id);

-- 检查现有索引的使用情况
-- 可以通过以下查询来分析索引使用情况：
/*
SHOW INDEX FROM dgdoc_archives_instance;

-- 分析慢查询日志
SHOW VARIABLES LIKE 'slow_query_log';
SHOW VARIABLES LIKE 'long_query_time';

-- 查看表的统计信息
SHOW TABLE STATUS LIKE 'dgdoc_archives_instance';
*/

-- 注意事项：
-- 1. 索引会占用额外的存储空间
-- 2. 索引会影响INSERT/UPDATE/DELETE的性能
-- 3. 建议在测试环境先验证索引效果
-- 4. 定期监控索引使用情况，删除不必要的索引
-- 5. 考虑使用 EXPLAIN 分析查询执行计划

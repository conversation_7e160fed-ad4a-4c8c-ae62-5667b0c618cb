package com.jky.anze.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jky.anze.domain.AnzeInsuranceEventRecord;
import com.jky.anze.domain.bo.DgOrgServiceQueryBo;
import com.jky.anze.domain.bo.DgdocInsuranceOrderBo;
import com.jky.anze.domain.vo.DganzeInsuranceServiceRecordVo;
import com.jky.anze.domain.vo.InsuranceOrderListVo;
import com.jky.anze.domain.vo.InsuranceRecordListVo;
import org.jeecg.modules.online.config.exception.BusinessException;

/**
 * @description:
 * @author: cq
 * @date: 2025/4/27 11:01
 */

public interface IInsuranceService {
    IPage<InsuranceOrderListVo> queryInsurancePage(Integer pageNo, Integer pageSize, DgdocInsuranceOrderBo bo) throws BusinessException;

    IPage<InsuranceRecordListVo> queryServiceRecordPage(Integer pageNo, Integer pageSize, DgOrgServiceQueryBo bo) throws BusinessException;

    DganzeInsuranceServiceRecordVo queryServiceRecordById(Long id);

    IPage<InsuranceRecordListVo> checkTodoList(Integer pageNo, Integer pageSize) throws BusinessException;

    Boolean deleteById(String id) throws BusinessException;

    Boolean auditByVo(InsuranceRecordListVo vo) throws BusinessException;

    Boolean insertServiceRecordFile(Long id, String fileUrl);

    Boolean delServiceRecordFile(Long id);

    IPage<AnzeInsuranceEventRecord> queryEventRecordList(Integer pageNo, Integer pageSize) throws BusinessException;
}

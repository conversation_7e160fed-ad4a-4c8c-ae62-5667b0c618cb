# Bean名称冲突修复说明

## 问题描述

在启动应用时遇到以下错误：

```
The bean 'taskExecutor', defined in class path resource [org/flowable/spring/boot/FlowableJobConfiguration.class], could not be registered. A bean with that name has already been defined in class path resource [com/jky/dgdoc/config/AsyncConfig.class] and overriding is disabled.
```

## 问题原因

- **Flowable框架**已经定义了一个名为`taskExecutor`的Bean用于工作流任务执行
- **我们的异步配置**也定义了一个同名的`taskExecutor` Bean用于档案状态计算
- Spring Boot默认禁用Bean覆盖，导致冲突

## 解决方案

### 1. 重命名我们的Bean

将我们的线程池Bean名称从`taskExecutor`改为`archivesTaskExecutor`：

**修改前：**
```java
@Bean("taskExecutor")
public Executor taskExecutor() {
    // ...
}
```

**修改后：**
```java
@Bean("archivesTaskExecutor")
public Executor archivesTaskExecutor() {
    // ...
}
```

### 2. 更新异步注解引用

同时更新所有使用该Bean的`@Async`注解：

**修改前：**
```java
@Async("taskExecutor")
public CompletableFuture<Void> computeParentStateAsync(...) {
    // ...
}
```

**修改后：**
```java
@Async("archivesTaskExecutor")
public CompletableFuture<Void> computeParentStateAsync(...) {
    // ...
}
```

## 修改的文件

1. **AsyncConfig.java**
   - Bean名称：`taskExecutor` → `archivesTaskExecutor`
   - 方法名称：`taskExecutor()` → `archivesTaskExecutor()`

2. **AsyncArchivesStateService.java**
   - `@Async("taskExecutor")` → `@Async("archivesTaskExecutor")`
   - 两个异步方法都需要更新

## 验证方法

启动应用后，可以通过以下方式验证修复是否成功：

1. **应用启动成功**：不再出现Bean冲突错误
2. **Bean注册正常**：可以在Spring上下文中找到两个不同的Bean
   - `taskExecutor`（Flowable的）
   - `archivesTaskExecutor`（我们的）

3. **功能正常**：档案删除操作的异步处理仍然工作正常

## 最佳实践

为了避免类似的Bean名称冲突，建议：

1. **使用具体的Bean名称**：避免使用通用名称如`taskExecutor`、`dataSource`等
2. **添加业务前缀**：如`archivesTaskExecutor`、`dgdocDataSource`等
3. **检查依赖框架**：在定义Bean前检查使用的框架是否已经定义了同名Bean
4. **使用IDE工具**：利用IDE的Bean依赖图来检查潜在冲突

## 注意事项

- 这个修改不会影响Flowable框架的正常工作
- 我们的异步档案状态计算功能保持不变
- 如果有其他地方直接引用了`taskExecutor` Bean，也需要相应更新

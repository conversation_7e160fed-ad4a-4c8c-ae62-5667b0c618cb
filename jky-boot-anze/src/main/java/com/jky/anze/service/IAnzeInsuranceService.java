package com.jky.anze.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jky.anze.domain.DganzeExperts;
import com.jky.anze.domain.DganzeInsuranceOrg;
import com.jky.anze.domain.DganzeInsuranceServiceRecord;
import com.jky.anze.domain.DganzeInsuranceWithFileUrl;
import com.jky.anze.domain.vo.DashboardStatsVo;
import com.jky.anze.domain.vo.DgdocAqjdConsSiteDetailVo;
import com.jky.dgdoc.domain.DgdocAqjdConsSite;
import com.jky.dgdoc.domain.vo.DgdocAqjdConsSiteVo;
import org.jeecg.modules.online.config.exception.BusinessException;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

public interface IAnzeInsuranceService {

    /**
     * 查询工地列表
     *
     * @param query
     * @param pageNo
     * @param pageSize
     * @return
     */
    IPage<DgdocAqjdConsSiteVo> queryConsSites(DgdocAqjdConsSiteVo query, Integer pageNo, Integer pageSize);

    /**
     * 检测是否上传保单、服务机构是否选择
     *
     * @param records
     */
    void queryInsuranceInfo(List<DgdocAqjdConsSiteVo> records);

    /**
     * 查询工地列表
     *
     * @param query
     * @param pageNo
     * @param pageSize
     * @return
     */
    IPage<DgdocAqjdConsSiteVo> list(DgdocAqjdConsSiteVo query, Integer pageNo, Integer pageSize);

    /**
     * 项目详情
     *
     * @param pdProjectCollect
     * @return
     */
    DgdocAqjdConsSiteDetailVo queryInsuranceDetailVo(DgdocAqjdConsSite pdProjectCollect);

    /**
     * 上传保单信息
     *
     * @param dganzeInsurance 保单信息
     * @return 是否上传成功
     */
    boolean uploadInsuranceInfo(DganzeInsuranceWithFileUrl dganzeInsuranceWithFileUrl);

    /**
     * 上传机构信息（支持批量）
     *
     * @param dganzeInsuranceOrgList 机构信息列表
     * @return 是否上传成功
     */
    boolean selectOrg(List<DganzeInsuranceOrg> dganzeInsuranceOrgList);

    /**
     * 提交服务记录
     *
     * @param record 服务记录
     * @return 是否提交成功
     */
    boolean serviceRecord(DganzeInsuranceServiceRecord record);

    /**
     * 根据机构ID获取专家列表
     *
     * @param orgId 机构ID
     * @return 专家列表
     */
    List<DganzeExperts> getExpertsByOrgId(String orgId);

    /**
     * 关联专家
     *
     * @param orgId    机构ID
     * @param expertId 专家ID
     * @return 是否关联成功
     */
    boolean addExpertRelation(String orgId, String expertId);

    /**
     * 删除专家关联
     *
     * @param orgId    机构ID
     * @param expertId 专家ID
     * @return 是否删除成功
     */
    boolean delExpertRelation(String orgId, String expertId);

    /**
     * 获取首页数据统计
     *
     * @return DashboardStatsVo
     */
    DashboardStatsVo getDashboardStats(String townshipId, String startTime, String endTime);

    /**
     * 供筛选专家列表
     */
    List<DganzeExperts> getAvailableExperts();

    void preview(String filePath, HttpServletResponse response) throws BusinessException, IOException;
}
package com.jky.anze.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @description:
 * @author: cq
 * @date: 2025/4/22 17:40
 */

@Data
@TableName("dganze_insurance_org")
public class DganzeInsuranceOrg implements Serializable {

    @TableId
    private Long insuranceOrgId;

    private String projectId;

    private String orgId;

    private String orgName;

    private String orgContract;

    private String orgAddress;

    private String orgPhone;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    private String updateBy;

    private String createBy;
}


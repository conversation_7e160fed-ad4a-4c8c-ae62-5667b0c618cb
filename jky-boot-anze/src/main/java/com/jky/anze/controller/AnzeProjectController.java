package com.jky.anze.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jky.anze.domain.DganzeExperts;
import com.jky.anze.domain.DganzeInsuranceOrg;
import com.jky.anze.domain.DganzeInsuranceServiceRecord;
import com.jky.anze.domain.DganzeInsuranceWithFileUrl;
import com.jky.anze.domain.vo.DashboardStatsVo;
import com.jky.anze.domain.vo.DgdocAqjdConsSiteDetailVo;
import com.jky.anze.service.IAnzeInsuranceService;
import com.jky.dgdoc.domain.DgdocAqjdConsSite;
import com.jky.dgdoc.domain.vo.DgdocAqjdConsSiteVo;
import com.jky.dgdoc.service.IDgdocAqjdConsSiteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.online.config.exception.BusinessException;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.io.IOException;
import java.util.List;

/**
 * @description:
 * @author: cq
 * @date: 2025/4/22 17:46
 */

@Validated
@Api(value = "项目管理", tags = {"项目管理"})
@RestController
@RequestMapping("/anze/project")
@RequiredArgsConstructor
public class AnzeProjectController {

    private final IDgdocAqjdConsSiteService dgdocAqjdConsSiteService;

    private final IAnzeInsuranceService insuranceService;

    /**
     * 查询工地列表
     */
    @ApiOperation("查询工地列表(dgdoc-project-list)")
    @RequiresPermissions("dgdoc-project-list")
    @GetMapping("/list")
    public Result<IPage<DgdocAqjdConsSiteVo>> list(DgdocAqjdConsSiteVo query, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "20") Integer pageSize) {
        IPage<DgdocAqjdConsSiteVo> page = insuranceService.list(query, pageNo, pageSize);
        Result<IPage<DgdocAqjdConsSiteVo>> result = new Result<>();
        result.setSuccess(true);
        result.setResult(page);
        return result;
    }

    /**
     * 查询工地信息
     */
    @ApiOperation("查询工地信息(dgdoc-project-query)")
    @RequiresPermissions("dgdoc-project-query")
    @GetMapping("/{consSiteId}")
    public Result<DgdocAqjdConsSiteDetailVo> queryById(@PathVariable("consSiteId") String consSiteId) {
        DgdocAqjdConsSite pdProjectCollect = dgdocAqjdConsSiteService.getById(consSiteId);

        return Result.OK(insuranceService.queryInsuranceDetailVo(pdProjectCollect));
    }

    /**
     * 上传保单
     */
    @ApiOperation("上传保单(dgdoc-project-uploadInsurance)")
    @RequiresPermissions("dgdoc-project-uploadInsurance")
    @PostMapping("/uploadInsurance")
    public Result<?> uploadInsurance(@RequestBody DganzeInsuranceWithFileUrl dganzeInsuranceWithFileUrl) {
        boolean flag = insuranceService.uploadInsuranceInfo(dganzeInsuranceWithFileUrl);
        if (flag) {
            return Result.OK("上传保单成功");
        } else {
            return Result.error("上传保单失败");
        }
    }

    /**
     * 选择服务机构
     */
    @ApiOperation("选择服务机构(dgdoc-service-association)")
    @RequiresPermissions("dgdoc-service-association")
    @PostMapping("/selectOrg")
    public Result<?> selectOrg(@RequestBody List<DganzeInsuranceOrg> orgList) {
        boolean flag = insuranceService.selectOrg(orgList);
        if (flag) {
            return Result.OK("提交成功");
        } else {
            return Result.error("提交失败");
        }
    }

    /**
     * 服务记录填写
     */
    @ApiOperation("服务记录填写(dgdoc-project-service-record)")
    @RequiresPermissions("dgdoc-project-service-record")
    @PostMapping("/serviceRecord")
    public Result<?> serviceRecord(@RequestBody DganzeInsuranceServiceRecord record) {
        boolean flag = insuranceService.serviceRecord(record);
        if (flag) {
            return Result.OK("提交成功");
        } else {
            return Result.error("提交失败");
        }
    }

    /**
     * 机构关联专家列表
     */
    @ApiOperation("机构关联专家列表")
    @RequiresPermissions("dgdoc-project-orgExperts")
    @GetMapping("/getExperts")
    public Result<List<DganzeExperts>> getExperts(@RequestParam("orgId") String orgId) {
        return Result.OK(insuranceService.getExpertsByOrgId(orgId));
    }

    /**
     * 供筛选专家列表
     */
    @ApiOperation("可筛选专家列表")
    @RequiresPermissions("dgdoc-project-availableExperts")
    @GetMapping("/availableExperts")
    public Result<List<DganzeExperts>> getAvailableExperts() {
        return Result.OK(insuranceService.getAvailableExperts());
    }

    /**
     * 专家关联
     */
    @ApiOperation("专家关联")
    @RequiresPermissions("dgdoc-project-addExpertRelation")
    @PostMapping("/addExpertRelation")
    public Result<?> addExpertRelation(@RequestParam("orgId") String orgId, @RequestParam("expertId") String expertId) {
        boolean flag = insuranceService.addExpertRelation(orgId, expertId);
        if (flag) {
            return Result.OK("关联成功");
        } else {
            return Result.error("关联失败");
        }
    }

    /**
     * 删除专家关联
     */
    @ApiOperation("删除专家关联")
    @RequiresPermissions("dgdoc-project-delExpertRelation")
    @PostMapping("/delExpertRelation")
    public Result<?> delExpertRelation(@RequestParam("orgId") String orgId, @RequestParam("expertId") String expertId) {
        boolean flag = insuranceService.delExpertRelation(orgId, expertId);
        if (flag) {
            return Result.OK("删除关联成功");
        } else {
            return Result.error("删除关联失败");
        }
    }

    /**
     * 首页数据统计
     */
    @ApiOperation("首页数据统计")
    @RequiresPermissions("dgdoc-project-dashboardStats")
    @GetMapping("/dashboardStats")
    public Result<DashboardStatsVo> dashboardStats(
            @RequestParam(value = "townshipId", required = false, defaultValue = "") String townshipId,
            @RequestParam(value = "startTime", required = false) String startTime,
            @RequestParam(value = "endTime", required = false) String endTime) {
        return Result.OK(insuranceService.getDashboardStats(townshipId, startTime, endTime));
    }

    @ApiOperation("预览图片或者PDF(dgdoc-anze-preview)")
    @RequiresPermissions("dgdoc-anze-preview")
    @GetMapping("/preview")
    public void preview(@NotBlank(message = "文件路径")@RequestParam("filePath") String filePath, HttpServletResponse response) throws BusinessException, IOException {
        insuranceService.preview(filePath, response);
    }
}


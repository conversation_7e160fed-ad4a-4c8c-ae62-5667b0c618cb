package com.jky.anze.domain.vo;

import com.jky.anze.domain.*;
import com.jky.dgdoc.domain.DgdocAqjdConsSite;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: cq
 * @date: 2025/4/23 8:49
 */
@Data
public class DgdocAqjdConsSiteDetailVo {


    private DgdocAqjdConsSite site;

    private DganzeInsurance insurance;

    private DganzeInsuranceFile insuranceFile;

    /**
     * 保单详情的服务机构
     */
    private List<DganzeInsuranceOrg> orgs = new ArrayList<>();

    /**
     * 机构详情的服务记录
     */
    private List<DganzeInsuranceServiceRecord> serviceRecords = new ArrayList<>();

    /**
     * 保单、监管部门详情的服务记录
     */
    private List<DganzeInsuranceOrgServiceRecord> orgServiceRecords = new ArrayList<>();
}


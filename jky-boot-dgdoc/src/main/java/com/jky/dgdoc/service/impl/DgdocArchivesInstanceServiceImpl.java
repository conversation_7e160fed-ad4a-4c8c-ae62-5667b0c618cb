package com.jky.dgdoc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.itextpdf.text.pdf.PdfReader;
import com.jky.dgdoc.domain.DgdocArchivesInstance;
import com.jky.dgdoc.domain.bo.CjEntConfirmBo;
import com.jky.dgdoc.domain.dto.MonomerArchivesDto;
import com.jky.dgdoc.domain.query.ArchivesProcessQuery;
import com.jky.dgdoc.domain.query.ArchivesQuery;
import com.jky.dgdoc.domain.vo.DgZjjgMonomerfileVo;
import com.jky.dgdoc.domain.vo.DgdocArchivesInstanceVo;
import com.jky.dgdoc.domain.vo.DgzjjzjJcjgCoreSampleTableVo;
import com.jky.dgdoc.domain.vo.MonomerVo;
import com.jky.dgdoc.domain.vo.PdMonomerCollectTableVo;
import com.jky.dgdoc.enums.ArchivesStatus;
import com.jky.dgdoc.enums.ArchivesType;
import com.jky.dgdoc.enums.AuditState;

import com.jky.dgdoc.enums.SupDealResult;
import com.jky.dgdoc.enums.UploadState;
import com.jky.dgdoc.mapper.DgdocArchivesInstanceMapper;
import com.jky.dgdoc.mapper.DgzjjzjJcjgCoreSampleMapper;
import com.jky.dgdoc.service.IDgdocArchivesInstanceService;
import com.jky.dgdoc.service.IDgdocMonomerService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.io.FilenameUtils;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.common.util.MinioUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.common.util.oConvertUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 档案实例接口
 */
@Service
@RequiredArgsConstructor
public class DgdocArchivesInstanceServiceImpl
        extends ServiceImpl<DgdocArchivesInstanceMapper, DgdocArchivesInstance> implements IDgdocArchivesInstanceService {

    @Value(value = "${jky.path.upload}")
    private String uploadpath;

    /**
     * 本地：local minio：minio 阿里：alioss
     */
    @Value(value = "${jky.uploadType}")
    private String uploadType;

    private final DgzjjzjJcjgCoreSampleMapper dgzjjzjJcjgCoreSampleMapper;


    @Override
    public Boolean cjEntConfirm(CjEntConfirmBo bo) {
        //上传文件限制PNG、JPG、JPEG、GIF、PDF格式，大小不超过10M
        Assert.isTrue(Objects.requireNonNull(bo.getConfirmFile().getContentType()).toLowerCase().contains("pdf") ||
                bo.getConfirmFile().getContentType().toLowerCase().contains("jpg") ||
                bo.getConfirmFile().getContentType().toLowerCase().contains("jpeg") ||
                bo.getConfirmFile().getContentType().toLowerCase().contains("gif") ||
                bo.getConfirmFile().getContentType().toLowerCase().contains("png"), "上传文件格式不正确");

        DgdocArchivesInstance dgdocArchivesInstance = baseMapper.selectOne(Wrappers.<DgdocArchivesInstance>lambdaQuery()
                .eq(DgdocArchivesInstance::getArchivesNo, bo.getArchivesNo())
                .eq(DgdocArchivesInstance::getProjectMonomerId, bo.getMonomerId())
                .eq(DgdocArchivesInstance::getArchivesType, ArchivesType.MONOMER.getCode()));
        Assert.notNull(dgdocArchivesInstance, "档案不存在");

        if (StringUtils.isNotBlank(dgdocArchivesInstance.getCjEntConfirmFile())) {
            throw new JeecgBootException("该档案参建单位已确认");
        }

        String uploadFile = uploadFile(bo.getConfirmFile(), "");

        if (StringUtils.isBlank(uploadFile)) {
            throw new JeecgBootException("上传失败");
        }
        dgdocArchivesInstance.setCjEntConfirmFile(uploadFile);

        return baseMapper.updateById(dgdocArchivesInstance) > 0;
    }

    @Override
    public Object processList(ArchivesProcessQuery query) {
        IDgdocMonomerService monomerService = SpringContextUtils.getBean(IDgdocMonomerService.class);

        List<MonomerArchivesDto> monomerArchivesDtos = new ArrayList<>();
        //状态为正常
        query.setStatus(ArchivesStatus.NORMAL.getCode());

        if (query.getArchivesType().equals(ArchivesType.PROJECT.getCode())) {
            ArchivesQuery archivesQuery = BeanUtil.toBean(query, ArchivesQuery.class);
            //设置项目ID
            archivesQuery.setProjectMonomerId(query.getProjectId());
            return treeList(archivesQuery);
        } else if (query.getArchivesType().equals(ArchivesType.MONOMER.getCode())) {
            //查询项目下所有单体
            List<PdMonomerCollectTableVo> monomerCollectTables = monomerService.queryList(query.getProjectId());

            if (CollectionUtil.isNotEmpty(query.getMonomerIds())) {
                //从搜索条件中过滤单体
                monomerCollectTables = monomerCollectTables.stream().filter(p -> query.getMonomerIds().contains(p.getMonomerId())).collect(Collectors.toList());
            }
            for (PdMonomerCollectTableVo monomerCollectTable : monomerCollectTables) {
                MonomerVo monomer = monomerService.queryById(monomerCollectTable.getMonomerId());
                //设置查询对的单体ID
                ArchivesQuery archivesQuery = BeanUtil.toBean(query, ArchivesQuery.class);
                //设置单体ID
                archivesQuery.setProjectMonomerId(monomer.getMonomerId());

                List<DgdocArchivesInstanceVo> archivesInstanceVos = treeList(archivesQuery);

                MonomerArchivesDto monomerArchivesDto = new MonomerArchivesDto();
                monomerArchivesDto.setMonomerId(monomer.getMonomerId());
                monomerArchivesDto.setMonomerName(monomer.getMonomerName());
                String monomerReportStatus = dgzjjzjJcjgCoreSampleMapper.queryMonomerJcjgUnqualified(monomer.getMonomerId());
                monomerArchivesDto.setMonomerReportStatus(monomerReportStatus);
                //查询销案状态
                List<DgzjjzjJcjgCoreSampleTableVo> dgzjjzjJcjgCoreSampleList = dgzjjzjJcjgCoreSampleMapper.queryMonomerJcjgList(monomer.getMonomerId());
                //判断是否全部销案
                boolean allMatch = dgzjjzjJcjgCoreSampleList.stream()
                        .filter(a -> ObjectUtil.isNotNull(a.getSupDealResult()))
                        .allMatch(a -> SupDealResult.CLOSED.getCode().equals(a.getSupDealResult()));
                if (allMatch) {
                    monomerArchivesDto.setMonomerSupDealResult("1"); // 已销案
                } else {
                    monomerArchivesDto.setMonomerSupDealResult("0"); // 未销案
                }
                monomerArchivesDto.setArchives(archivesInstanceVos);
                monomerArchivesDtos.add(monomerArchivesDto);
            }

        }
        return monomerArchivesDtos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeFile(String archivesInstanceId) {
        DgdocArchivesInstance dgdocArchivesInstance = baseMapper.selectById(archivesInstanceId);
        if (dgdocArchivesInstance.getUploadState().equals(UploadState.PARTUPLOAD.getCode())) {
            throw new JeecgBootException("该档案未上传");
        }
        if (dgdocArchivesInstance.getAuditState().equals(AuditState.PASS.getCode())) {
            throw new JeecgBootException("该档案已审核不能删除");
        }
        LambdaUpdateWrapper<DgdocArchivesInstance> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(DgdocArchivesInstance::getArchivesId, dgdocArchivesInstance.getArchivesId());

        updateWrapper.set(DgdocArchivesInstance::getUploadState, UploadState.UNUPLOAD.getCode());
        updateWrapper.set(DgdocArchivesInstance::getFileUrl, null);
        updateWrapper.set(DgdocArchivesInstance::getFileType, null);
        updateWrapper.set(DgdocArchivesInstance::getFileSize, BigDecimal.ZERO);
        updateWrapper.set(DgdocArchivesInstance::getFilePage, null);
        updateWrapper.set(DgdocArchivesInstance::getUploadDate, null);
        int i = baseMapper.update(null, updateWrapper);

        this.computeParentState(dgdocArchivesInstance.getArchivesId(), dgdocArchivesInstance.getProjectMonomerId(), dgdocArchivesInstance.getArchivesType(), UploadState.UNUPLOAD.getCode(), null);

        //TODO 删除OSS档案文件
        //OssBootUtil.deleteUrl(gddocArchivesInstance.getFileUrl());
        return i > 0;
    }

    @Override
    @Transactional
    public Boolean updateStatus(String archivesInstanceId, String status) {
        // 校验状态 是否是作废或恢复
        if (!(ArchivesStatus.NORMAL.getCode().equals(status) || ArchivesStatus.INVALID.getCode().equals(status))) {
            throw new JeecgBootException("状态不正确");
        }
        DgdocArchivesInstance dgdocArchivesInstance = baseMapper.selectById(archivesInstanceId);


        dgdocArchivesInstance.setStatus(status);
        boolean flag = baseMapper.updateById(dgdocArchivesInstance) > 0;
        if (flag) {
            //如果是子节点，需要判断同级节点是否全部作废，如果全部作废则父节点也作废
            this.updateParentStatus(dgdocArchivesInstance);
            //重新计算上传状态
            this.computeParentState(dgdocArchivesInstance.getArchivesId(), dgdocArchivesInstance.getProjectMonomerId(), dgdocArchivesInstance.getArchivesType(), UploadState.UNUPLOAD.getCode(), null);
        }
        return flag;
    }

    private void updateParentStatus(DgdocArchivesInstance dgdocArchivesInstance) {
        if (StringUtils.isNotBlank(dgdocArchivesInstance.getParentId())) {
            LambdaQueryWrapper<DgdocArchivesInstance> qw = Wrappers.lambdaQuery();
            qw.eq(DgdocArchivesInstance::getParentId, dgdocArchivesInstance.getParentId());
            qw.eq(DgdocArchivesInstance::getProjectMonomerId, dgdocArchivesInstance.getProjectMonomerId());
            qw.eq(DgdocArchivesInstance::getArchivesType, dgdocArchivesInstance.getArchivesType());
            List<DgdocArchivesInstance> archivesInstances = baseMapper.selectList(qw);

            if (archivesInstances.isEmpty()) {
                return;
            }
            LambdaUpdateWrapper<DgdocArchivesInstance> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.eq(DgdocArchivesInstance::getProjectMonomerId, dgdocArchivesInstance.getProjectMonomerId());
            updateWrapper.eq(DgdocArchivesInstance::getArchivesType, dgdocArchivesInstance.getArchivesType());

            if (archivesInstances.stream().allMatch(p -> ArchivesStatus.INVALID.getCode().equals(p.getStatus()))) {
                //更新父节点状态
                updateWrapper.eq(DgdocArchivesInstance::getArchivesId, dgdocArchivesInstance.getParentId());
                updateWrapper.set(DgdocArchivesInstance::getStatus, ArchivesStatus.INVALID.getCode());
                baseMapper.update(null, updateWrapper);
            }
            //如果是恢复父节点，则子节点全部恢复
            if (ArchivesStatus.NORMAL.getCode().equals(dgdocArchivesInstance.getStatus())) {
                updateWrapper.eq(DgdocArchivesInstance::getParentId, dgdocArchivesInstance.getArchivesId());
                updateWrapper.set(DgdocArchivesInstance::getStatus, ArchivesStatus.NORMAL.getCode());
                baseMapper.update(null, updateWrapper);
            }

        }

    }

    @Override
    @SneakyThrows
    public void preview(String archivesInstanceId, HttpServletResponse response) {
        DgdocArchivesInstance dgdocArchivesInstance = baseMapper.selectById(archivesInstanceId);
        if (dgdocArchivesInstance == null) {
            throw new RuntimeException("档案不存在");
        }
        //从地址中获取inputStream
        String pre = MinioUtil.getMinioUrl() + MinioUtil.getBucketName() + "/";
        InputStream in = MinioUtil.getMinioFile(MinioUtil.getBucketName(), dgdocArchivesInstance.getFileUrl()
                .replace(pre, ""));

        responseFile(response, in, dgdocArchivesInstance.getFileType());
    }


    /**
     * 响应文件
     *
     * @param response
     * @throws IOException
     */
    private synchronized void responseFile(HttpServletResponse response, InputStream in, String fileType) throws IOException {
        if (in == null) {
            throw new JeecgBootException("获取文件流失败");
        }

        if ("jpg".equals(fileType) || "jpeg".equals(fileType) || "gif".equals(fileType) || "png".equals(fileType)) {
            response.setContentType("image/" + fileType);
        } else if ("pdf".equals(fileType)) {
            response.setContentType("application/pdf");
        } else {
            response.setContentType("multipart/form-data");
        }

        try (ServletOutputStream out = response.getOutputStream()) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = in.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
            }
            out.flush();
        } finally {
            in.close();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean upload(String archivesInstanceId, HttpServletRequest request) {
        String savePath = "";
        String bizPath = request.getParameter("biz");
        //LOWCOD-2580 sys/common/upload接口存在任意文件上传漏洞
        if (oConvertUtils.isNotEmpty(bizPath) && (bizPath.contains("../") || bizPath.contains("..\\"))) {
            throw new JeecgBootException("上传目录bizPath，格式非法！");
        }
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        MultipartFile file = multipartRequest.getFile("file");// 获取上传文件对象

        savePath = uploadFile(file, bizPath);
        DgdocArchivesInstance dgdocArchivesInstance = baseMapper.selectById(archivesInstanceId);
        dgdocArchivesInstance.setFileType(FilenameUtils.getExtension(file.getOriginalFilename()));

        if (Objects.requireNonNull(file.getContentType()).toLowerCase().contains("pdf")) {
            dgdocArchivesInstance.setFilePage(getFilePage(file));
        } else {
            dgdocArchivesInstance.setFilePage(1);
        }
        dgdocArchivesInstance.setFileSize(BigDecimal.valueOf(file.getSize()));
        dgdocArchivesInstance.setFileUrl(savePath);
        dgdocArchivesInstance.setUploadState(UploadState.UPLOADED.getCode());
        dgdocArchivesInstance.setUploadDate(new Date());

        // 计算父节点的上传状态
        this.computeParentState(dgdocArchivesInstance.getArchivesId(), dgdocArchivesInstance.getProjectMonomerId(), dgdocArchivesInstance.getArchivesType(), UploadState.UPLOADED.getCode(), null);
        //如果审核状态为退回，则将审核状态改为未审核
        if (AuditState.RETURN.getCode().equals(dgdocArchivesInstance.getAuditState())) {
            dgdocArchivesInstance.setAuditState(AuditState.UNAUDITED.getCode());
        }
        return baseMapper.updateById(dgdocArchivesInstance) > 0;
    }

    private String uploadFile(MultipartFile file, String bizPath) {
        String savePath;
        Assert.notNull(file, "上传文件为空");
        if (oConvertUtils.isEmpty(bizPath)) {
            if (CommonConstant.UPLOAD_TYPE_OSS.equals(uploadType)) {
                //未指定目录，则用阿里云默认目录 upload
                bizPath = "upload";
                //result.setMessage("使用阿里云文件上传时，必须添加目录！");
                //result.setSuccess(false);
                //return result;
            } else {
                bizPath = "";
            }
        }
        if (CommonConstant.UPLOAD_TYPE_LOCAL.equals(uploadType)) {
            //update-begin-author:lvdandan date:20200928 for:修改JEditor编辑器本地上传
            savePath = this.uploadLocal(file, bizPath);
            //update-begin-author:lvdandan date:20200928 for:修改JEditor编辑器本地上传
            /**  富文本编辑器及markdown本地上传时，采用返回链接方式
             //针对jeditor编辑器如何使 lcaol模式，采用 base64格式存储
             String jeditor = request.getParameter("jeditor");
             if(oConvertUtils.isNotEmpty(jeditor)){
             result.setMessage(CommonConstant.UPLOAD_TYPE_LOCAL);
             result.setSuccess(true);
             return result;
             }else{
             savePath = this.uploadLocal(file,bizPath);
             }
             */
        } else {
            //update-begin-author:taoyan date:20200814 for:文件上传改造
            savePath = CommonUtils.upload(file, bizPath, uploadType);
            //update-end-author:taoyan date:20200814 for:文件上传改造
        }
        return savePath;
    }

    /**
     * MultipartFile:pdf文件
     */
    public static int getFilePage(MultipartFile multipartFile) {
        try {
            PdfReader pdfReader = new PdfReader(multipartFile.getInputStream());
            //pdf页数
            int pdfPage = pdfReader.getNumberOfPages();
            return pdfPage;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return 0;
    }


    /**
     * 计算父节点的上传状态和审核状态
     *
     * @param uploadState
     * @param auditState
     */
    @Transactional(rollbackFor = Exception.class)
    public void computeParentState(String archivesId, String projectMonomerId, String archivesType, String uploadState, String auditState) {
        LambdaQueryWrapper<DgdocArchivesInstance> qw1 = Wrappers.lambdaQuery();
        qw1.eq(DgdocArchivesInstance::getArchivesId, archivesId);
        qw1.eq(DgdocArchivesInstance::getProjectMonomerId, projectMonomerId);
        qw1.eq(DgdocArchivesInstance::getArchivesType, archivesType);

        DgdocArchivesInstance dgdocArchivesInstance = baseMapper.selectOne(qw1);
        if (dgdocArchivesInstance == null) {
            return;
        }
        if (StringUtils.isNotBlank(auditState)) {
            dgdocArchivesInstance.setAuditState(auditState);
        }
        if (StringUtils.isNotBlank(uploadState)) {
            dgdocArchivesInstance.setUploadState(uploadState);
        }
        baseMapper.updateById(dgdocArchivesInstance);

        if (StringUtils.isNotBlank(dgdocArchivesInstance.getParentId())) {
            LambdaQueryWrapper<DgdocArchivesInstance> qw = Wrappers.lambdaQuery();
            qw.eq(DgdocArchivesInstance::getParentId, dgdocArchivesInstance.getParentId());
            qw.eq(DgdocArchivesInstance::getProjectMonomerId, dgdocArchivesInstance.getProjectMonomerId());
            qw.eq(DgdocArchivesInstance::getArchivesType, dgdocArchivesInstance.getArchivesType());
            qw.eq(DgdocArchivesInstance::getStatus, ArchivesStatus.NORMAL.getCode());
            List<DgdocArchivesInstance> archivesInstances = baseMapper.selectList(qw);

            if (archivesInstances.isEmpty()) {
                return;
            }
            //更新上传状态
            if (StringUtils.isNotBlank(uploadState)) {
                String thisUploadState = null;
                if (archivesInstances.stream().allMatch(p -> UploadState.UPLOADED.getCode().equals(p.getUploadState()))) {
                    thisUploadState = UploadState.UPLOADED.getCode();
                } else if (archivesInstances.stream().allMatch(p -> UploadState.UNUPLOAD.getCode().equals(p.getUploadState()))) {
                    thisUploadState = UploadState.UNUPLOAD.getCode();
                } else if (archivesInstances.stream().anyMatch(p -> UploadState.UNUPLOAD.getCode().equals(p.getUploadState()))) {
                    thisUploadState = UploadState.PARTUPLOAD.getCode();
                }
                this.computeParentState(dgdocArchivesInstance.getParentId(), dgdocArchivesInstance.getProjectMonomerId(), dgdocArchivesInstance.getArchivesType(), thisUploadState, null);

            }
            //更新审核状态
            if (StringUtils.isNotBlank(auditState)) {
                String thisAuditState = null;
                if (archivesInstances.stream().allMatch(p -> AuditState.PASS.getCode().equals(p.getAuditState()))) {
                    thisAuditState = AuditState.PASS.getCode();
                } else if (archivesInstances.stream().allMatch(p -> AuditState.UNAUDITED.getCode().equals(p.getAuditState()))) {
                    thisAuditState = AuditState.UNAUDITED.getCode();
                } else if (archivesInstances.stream().anyMatch(p -> AuditState.UNAUDITED.getCode().equals(p.getAuditState()))) {
                    thisAuditState = AuditState.PARTAUDITED.getCode();
                }
                this.computeParentState(dgdocArchivesInstance.getParentId(), dgdocArchivesInstance.getProjectMonomerId(), dgdocArchivesInstance.getArchivesType(), null, thisAuditState);
            }
        }
    }

    /**
     * 本地文件上传
     *
     * @param mf      文件
     * @param bizPath 自定义路径
     * @return
     */
    private String uploadLocal(MultipartFile mf, String bizPath) {
        try {
            String ctxPath = uploadpath;
            String fileName = null;
            File file = new File(ctxPath + File.separator + bizPath + File.separator);
            if (!file.exists()) {
                file.mkdirs();// 创建文件根目录
            }
            String orgName = mf.getOriginalFilename();// 获取文件名
            orgName = CommonUtils.getFileName(orgName);
            if (orgName.indexOf(".") != -1) {
                fileName = orgName.substring(0, orgName.lastIndexOf(".")) + "_" + System.currentTimeMillis() + orgName.substring(orgName.lastIndexOf("."));
            } else {
                fileName = orgName + "_" + System.currentTimeMillis();
            }
            String savePath = file.getPath() + File.separator + fileName;
            File savefile = new File(savePath);
            FileCopyUtils.copy(mf.getBytes(), savefile);
            String dbpath = null;
            if (oConvertUtils.isNotEmpty(bizPath)) {
                dbpath = bizPath + File.separator + fileName;
            } else {
                dbpath = fileName;
            }
            if (dbpath.contains("\\")) {
                dbpath = dbpath.replace("\\", "/");
            }
            return dbpath;
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return "";
    }

    @Override
    @Transactional
    public Boolean returnBack(String archivesInstanceId) {
        DgdocArchivesInstance dgdocArchivesInstance = baseMapper.selectById(archivesInstanceId);

        if (AuditState.PASS.getCode().equals(dgdocArchivesInstance.getAuditState())) {
            throw new RuntimeException("该档案已审核通过");
        }
        if (AuditState.RETURN.getCode().equals(dgdocArchivesInstance.getAuditState())) {
            throw new RuntimeException("该档案已退回");
        }
        dgdocArchivesInstance.setAuditState(AuditState.RETURN.getCode());
        //dgdocArchivesInstance.setReturnBackReason(bo.getReturnBackReason());
        //dgdocArchivesInstance.setReturnBackRemark(bo.getReturnBackRemark());
        return updateById(dgdocArchivesInstance);
    }

    @Override
    @Transactional
    public Boolean audit(List<String> archivesInstanceId) {
        List<DgdocArchivesInstance> dgdocArchivesInstances = baseMapper.selectBatchIds(archivesInstanceId);
        for (DgdocArchivesInstance dgdocArchivesInstance : dgdocArchivesInstances) {
            if (AuditState.PASS.getCode().equals(dgdocArchivesInstance.getAuditState())) {
                throw new RuntimeException("该档案已审核");
            }
            dgdocArchivesInstance.setAuditState(AuditState.PASS.getCode());
            // 计算父节点的审核状态
            this.computeParentState(dgdocArchivesInstance.getArchivesId(), dgdocArchivesInstance.getProjectMonomerId(), dgdocArchivesInstance.getArchivesType(), null, AuditState.PASS.getCode());
        }

        return updateBatchById(dgdocArchivesInstances);
    }

    @Override
    public List<DgdocArchivesInstanceVo> treeList(ArchivesQuery query) {
        Assert.notNull(query.getProjectMonomerId(), "项目ID不能为空");
        Assert.notNull(query.getArchivesType(), "档案类型不能为空");

        List<DgdocArchivesInstanceVo> archivesInstances = this.baseMapper.queryList(query);
        //查询附件，archives_type = '1' AND parent_id IN('A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M')
        String[] parentIds = {"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M"};
        for (DgdocArchivesInstanceVo archivesInstance : archivesInstances) {
            if (ArchivesType.MONOMER.getCode().equals(archivesInstance.getArchivesType()) && Arrays.stream(parentIds).anyMatch(id -> id.equals(archivesInstance.getParentId()))) {
                List<DgZjjgMonomerfileVo> fileList = this.baseMapper.queryFileList(archivesInstance.getArchivesInstanceId());
                archivesInstance.setFileList(fileList);
            }
        }

        // 构建树结构数据返回 父节点ID为大写字母的为根节点
        List<DgdocArchivesInstanceVo> trees = archivesInstances.stream()
                .filter(p -> StringUtils.isNotBlank(p.getParentId()) && p.getParentId().matches("[A-Z]+"))
                .peek(treeEntity -> treeEntity.setChildren(getChildren(treeEntity, archivesInstances))).collect(Collectors.toList());


        return searchTree(trees, query);
    }

    /**
     * 搜索树
     *
     * @param data
     * @param bo
     * @return
     */
    public static List<DgdocArchivesInstanceVo> searchTree(List<DgdocArchivesInstanceVo> data, ArchivesQuery bo) {
        List<DgdocArchivesInstanceVo> res = new ArrayList<>();

        for (DgdocArchivesInstanceVo treeNode : data) {
            List<DgdocArchivesInstanceVo> children = treeNode.getChildren();

            if (CollectionUtil.isNotEmpty(children)) {
                // 搜索父节点
                if (matchesCriteria(treeNode, bo)) {
                    res.add(treeNode);
                    continue;
                }
                // 递归搜索子节点
                List<DgdocArchivesInstanceVo> searchData = searchTree(children, bo);

                DgdocArchivesInstanceVo tn = BeanUtil.toBean(treeNode, DgdocArchivesInstanceVo.class);
                tn.setChildren(searchData);

                // 只有在当前节点或其子节点匹配任一搜索条件时才添加到结果列表
                if (matchesCriteria(tn, bo) || !searchData.isEmpty()) {
                    res.add(tn);
                }
            } else {
                // 没有子节点，检查当前节点是否符合搜索条件
                if (matchesCriteria(treeNode, bo)) {
                    res.add(treeNode);
                }
            }
        }

        return res;
    }

    /**
     * 搜索条件
     *
     * @param node
     * @param bo
     * @return
     */
    private static boolean matchesCriteria(DgdocArchivesInstanceVo node, ArchivesQuery bo) {
        return (StringUtils.isBlank(bo.getArchivesName()) || node.getArchivesName().contains(bo.getArchivesName())) &&
                (StringUtils.isBlank(bo.getUploadState()) || node.getUploadState().equals(bo.getUploadState())) &&
                (StringUtils.isBlank(bo.getAuditState()) || node.getAuditState().equals(bo.getAuditState()));
    }

    /**
     * 递归获取子节点
     */
    private static List<DgdocArchivesInstanceVo> getChildren(DgdocArchivesInstanceVo treeEntity, List<DgdocArchivesInstanceVo> list) {
        List<DgdocArchivesInstanceVo> children = list.stream().filter(p -> StringUtils.isNotBlank(p.getParentId()) && p.getParentId().equals(treeEntity.getArchivesId()))
                .peek(menu -> menu.setChildren(getChildren(menu, list)))
                .collect(Collectors.toList());
        //如果是作废的子节点设置null
        if (ArchivesStatus.INVALID.getCode().equals(treeEntity.getStatus())) {
            return null;
        }

        //子节点为空时，返回null
        if (children.isEmpty()) {
            return null;
        }

        //计算子等上传日期
        Date uploadDate = children.stream().map(DgdocArchivesInstanceVo::getUploadDate).filter(Objects::nonNull).max(Date::compareTo).orElse(null);
        treeEntity.setUploadDate(uploadDate);
        //计算子等上传页数
        Integer filePage = children.stream().map(DgdocArchivesInstanceVo::getFilePage).filter(Objects::nonNull).reduce(0, Integer::sum);
        treeEntity.setFilePage(filePage == 0 ? null : filePage);
        return children;
    }
}

package com.jky.dgdoc.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class DgdocAqjdConsSiteVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 数据主键
     */
    private String id;

    /**
     * 项目安全监督注册号（项目编号）
     */
    private String consSiteSsRegNo;

    /**
     * 工地名称（项目名称）
     */
    private String consSiteName;

    /**
     * 工地地点（项目地点）
     */
    private String consSiteAddr;

    /**
     * 镇街id（所属镇街ID）
     */
    private String townshipId;

    /**
     * 镇街名称（所属镇街）
     */
    private String townshipName;

    /**
     * 建设性质
     */
    private String consProp;

    /**
     * 建设规模/平方米
     */
    private Double consSize;

    /**
     * 建设规模/Km
     */
    private Double consLength;

    /**
     * 合同价格（万元）
     */
//    private Double agreementPrice;

    /**
     * 工程造价（万元）
     */
//    private Double projectCost;

    /**
     * 监督等级
     */
    private String supLevel;

    /**
     * 投资性质
     */
    private String investNature;

    /**
     * 项目状态
     */
    private String projectStatus;

    /**
     * 项目ID（关联项目ID）
     */
    private String projectId;

    /**
     * 合同工期起始时间
     */
    private LocalDateTime agreementStartTime;

    /**
     * 合同工期结束时间
     */
    private LocalDateTime agreementEndTime;

    /**
     * 建设单位ID
     */
    private String developOrgId;

    /**
     * 工程类型
     */
    private String projectType;

    /**
     * 特殊类型
     */
    private String itemLabelType;

    /**
     * 重点项目
     */
    private String keyProject;

    /**
     * 项目代码（关联项目代码）
     */
    private String projectNo;

    /**
     * 工地类型
     */
    private String consSiteType;

    /**
     * 建设单位统一社会信用代码
     */
    private String developCreditCode;

    /**
     * 建设单位名称
     */
    private String developOrgName;

    /**
     * 删除标记
     */
    private String signDeleted;

    /**
     * 最后更新时间
     */
    private LocalDateTime updatetime;

    /**
     * 数据全字段MD5
     */
//    private String dataMd5;

    /**
     * 数据标识
     */
//    private String zzDataFlag;

    /**
     * 数据同步时间（自动更新）
     */
//    private LocalDateTime jkSyncTime;

//    @TableField(exist = false)
    private String projectName;

    @ApiModelProperty("是否有保单")
    private boolean hasEnsuranceOrder;

    @ApiModelProperty("是否选择机构")
    private boolean hasOrg;

    /**
     * 是否是低合同金额 0否 1是
     */
    private Integer isLowContractAmount;

    /**
     * 关联保单id
     */
    private Long insuranceId;

    /**
     * 审核状态
     */
    private String auditState;

    /**
     * 保险公司关联项目 0-全部项目 1-关联项目
     */
    private Integer insuranceProject;
}
package com.jky.anze.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jky.anze.domain.DganzeStateMaintain;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

public interface DganzeStateMaintainMapper extends BaseMapper<DganzeStateMaintain> {
    @Update("INSERT INTO dganze_insurance_state_maintain (project_id, has_insurance_order) " +
            "VALUES (#{projectId}, 1) " +
            "ON DUPLICATE KEY UPDATE has_insurance_order = 1")
    void upsertHasInsuranceOrder(@Param("projectId") String projectId);

    @Update("INSERT INTO dganze_insurance_state_maintain (project_id, has_org) " +
            "VALUES (#{projectId}, 1) " +
            "ON DUPLICATE KEY UPDATE has_org = 1")
    void upsertHasOrg(@Param("projectId") String projectId);

    @Update("INSERT INTO dganze_insurance_state_maintain (project_id, report_status) " +
            "VALUES (#{projectId}, 1) " +
            "ON DUPLICATE KEY UPDATE report_status = 1")
    void upsertReportStatus(@Param("projectId") String projectId);

}

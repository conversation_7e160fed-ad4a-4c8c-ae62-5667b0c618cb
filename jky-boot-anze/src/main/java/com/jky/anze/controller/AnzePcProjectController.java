package com.jky.anze.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jky.anze.domain.bo.DgdocPcProjectBo;
import com.jky.anze.domain.bo.RecordResultBo;
import com.jky.anze.domain.vo.DgPcProjectItemVo;
import com.jky.anze.domain.vo.PcProjectDetailVo;
import com.jky.anze.service.IPcProjectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.online.config.exception.BusinessException;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: cq
 * @date: 2025/4/30 16:51
 */
@Validated
@Api(value = "PC端项目管理", tags = {"PC端项目管理"})
@RestController
@RequestMapping("/anze/pc")
@RequiredArgsConstructor
public class AnzePcProjectController {

    private final IPcProjectService pcProjectService;


    @ApiOperation("PC端项目管理服务列表(dgdoc-pc-list)")
    @RequiresPermissions("dgdoc-pc-list")
    @GetMapping("/list")
    public Result<IPage<DgPcProjectItemVo>> list(DgdocPcProjectBo bo, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "20") Integer pageSize) throws BusinessException {
        IPage<DgPcProjectItemVo> page = pcProjectService.queryProjectItemVo(bo, pageNo, pageSize);
        Result<IPage<DgPcProjectItemVo>> result = new Result<>();
        result.setSuccess(true);
        result.setResult(page);
        return result;
    }

    @ApiOperation("服务类型详情列表（dgdoc-pc-detail）")
    @RequiresPermissions("dgdoc-pc-detail")
    @GetMapping("/detail/{id}")
    public Result<Map<Integer, List<PcProjectDetailVo>>> detail(@PathVariable("id") String id) {
        Map<Integer, List<PcProjectDetailVo>> r = pcProjectService.eventRecordList(id);
        Result<Map<Integer, List<PcProjectDetailVo>>> result = new Result<>();
        result.setSuccess(true);
        result.setResult(r);
        return result;
    }

    @ApiOperation("服务报告上传文件（dgdoc-pc-upload）")
    @RequiresPermissions("dgdoc-pc-upload")
    @PostMapping("/upload")
    public Result<Boolean> upload(@RequestBody RecordResultBo bo) throws BusinessException {
        Result<Boolean> result = new Result<>();
        result.setSuccess(true);
        result.setResult(pcProjectService.uploadResult(bo));
        return result;
    }
}


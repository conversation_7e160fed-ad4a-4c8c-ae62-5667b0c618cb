package com.jky.anze.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @description:
 * @author: cq
 * @date: 2025/4/27 10:53
 */
@ApiModel("保单历史记录（保司）")
@Data
public class InsuranceOrderListVo {


    @ApiModelProperty("项目ID")
    private String projectId;
    @ApiModelProperty("项目名称")
    private String name;

    @ApiModelProperty("上传日期")
    private Date uploadTime;

    @ApiModelProperty("保单编号")
    private String orderNo;
}


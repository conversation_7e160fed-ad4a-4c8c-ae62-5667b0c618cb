package com.jky.anze.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @description:
 * @author: cq
 * @date: 2025/4/28 14:42
 */

@Data
@TableName("dganze_insurance_event_record")
public class AnzeInsuranceEventRecord implements Serializable {

    @TableId("id")
    private String id;

    private String recordContent;

    private String projectId;
    private String contentId;

    private String recordTitle;

    private String recordStatus;

    private Integer recordType;

    private Date createTime;

    private String orgId;

    private String userId;
}


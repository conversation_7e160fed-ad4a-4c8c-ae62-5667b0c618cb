package com.jky.anze.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jky.anze.domain.AnzeInsuranceEventRecord;
import com.jky.anze.domain.bo.DgOrgServiceQueryBo;
import com.jky.anze.domain.bo.DgdocInsuranceOrderBo;
import com.jky.anze.domain.vo.DganzeInsuranceServiceRecordVo;
import com.jky.anze.domain.vo.InsuranceOrderListVo;
import com.jky.anze.domain.vo.InsuranceRecordListVo;
import com.jky.anze.service.IInsuranceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.online.config.exception.BusinessException;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description:
 * @author: cq
 * @date: 2025/4/27 10:41
 */
@Validated
@Api(value = "保单与服务", tags = {"项目保单与服务管理"})
@RestController
@RequestMapping("/anze/insurance")
@RequiredArgsConstructor
public class InsuranceServiceController {


    private final IInsuranceService insuranceService;

    @GetMapping("/order")
    @ApiOperation("保司保单历史列表(dgdoc-insurance-order)")
    @RequiresPermissions("dgdoc-insurance-order")
    public Result<IPage<InsuranceOrderListVo>> queryInsurancePage(
            DgdocInsuranceOrderBo bo,
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "300") Integer pageSize
    ) throws BusinessException {
        IPage<InsuranceOrderListVo> page = insuranceService.queryInsurancePage(pageNo, pageSize, bo);
        Result<IPage<InsuranceOrderListVo>> result = new Result<>();
        result.setSuccess(true);
        result.setResult(page);
        return result;
    }


    @GetMapping("/server")
    @ApiOperation("服务机构服务列表(dgdoc-insurance-server)")
    @RequiresPermissions("dgdoc-insurance-server")
    public Result<IPage<InsuranceRecordListVo>> queryServerOrgList(
            DgOrgServiceQueryBo bo,
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "300") Integer pageSize
    ) throws BusinessException {
        IPage<InsuranceRecordListVo> page = insuranceService.queryServiceRecordPage(pageNo, pageSize, bo);
        Result<IPage<InsuranceRecordListVo>> result = new Result<>();
        result.setSuccess(true);
        result.setResult(page);
        return result;
    }

    @GetMapping("/server/{id}")
    @ApiOperation("服务机构服务详情(dgdoc-insurance-sdetail)")
    @RequiresPermissions("dgdoc-insurance-sdetail")
    public Result<DganzeInsuranceServiceRecordVo> queryById(@PathVariable Long id) {
        Result<DganzeInsuranceServiceRecordVo> result = new Result<>();
        result.setSuccess(true);
        result.setResult(insuranceService.queryServiceRecordById(id));
        return result;
    }

    @PostMapping("/server/upload/{id}")
    @ApiOperation("服务机构上传服务结果文件(dgdoc-insurance-upload)")
    @RequiresPermissions("dgdoc-insurance-upload")
    public Result<Boolean> upload(@PathVariable Long id, @RequestParam("fileUrl") String fileUrl) {
        Result<Boolean> result = new Result<>();
        result.setSuccess(true);
        result.setResult(insuranceService.insertServiceRecordFile(id, fileUrl));
        return result;
    }

    @DeleteMapping("/server/upload/{id}")
    @ApiOperation("服务机构删除服务结果文件(dgdoc-insurance-upload)")
    @RequiresPermissions("dgdoc-insurance-upload")
    public Result<Boolean> delete(@PathVariable Long id) {
        Result<Boolean> result = new Result<>();
        result.setSuccess(true);
        result.setResult(insuranceService.delServiceRecordFile(id));
        return result;
    }

    @GetMapping("/audit/list")
    @ApiOperation("审核列表(dgdoc-insurance-todo)")
    @RequiresPermissions("dgdoc-insurance-todo")
    public Result<IPage<InsuranceRecordListVo>> checkerList(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "300") Integer pageSize) throws BusinessException {
        IPage<InsuranceRecordListVo> page = insuranceService.checkTodoList(pageNo, pageSize);
        Result<IPage<InsuranceRecordListVo>> result = new Result<>();
        result.setSuccess(true);
        result.setResult(page);
        return result;
    }

    @DeleteMapping("/audit/{id}")
    @ApiOperation("删除审核(dgdoc-insurance-del)")
    @RequiresPermissions("dgdoc-insurance-del")
    public Result<Boolean> deleteById(@PathVariable String id) throws BusinessException {
        Result<Boolean> result = new Result<>();
        result.setSuccess(true);
        result.setResult(insuranceService.deleteById(id));
        return result;
    }

    @PutMapping("/audit/todo")
    @ApiOperation("审核记录(dgdoc-insurance-audit)")
    @RequiresPermissions("dgdoc-insurance-audit")
    public Result<Boolean> audit(@RequestBody InsuranceRecordListVo vo) throws BusinessException {
        Result<Boolean> result = new Result<>();
        result.setSuccess(true);
        result.setResult(insuranceService.auditByVo(vo));
        return result;
    }


    @GetMapping("event/record/list")
    @ApiOperation("首页动态（dgdoc-event-list）")
    @RequiresPermissions("dgdoc-event-list")
    public Result<IPage<AnzeInsuranceEventRecord>> listEvent(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "300") Integer pageSize) throws BusinessException {
        Result<IPage<AnzeInsuranceEventRecord>> result = new Result<>();
        result.setSuccess(true);
        result.setResult(insuranceService.queryEventRecordList(pageNo, pageSize));
        return result;
    }
}


<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jky.anze.mapper.DganzeInsuranceServiceRecordMapper">


    <select id="queryServiceTodoList" resultType="com.jky.anze.domain.vo.InsuranceRecordListVo">
        select m.project_id as projectId,
        n.cons_site_name as projectName,
        m.id as id,
        m.audit_state as auditState,
        m.service_time as serviceDate,
        m.service_type as serviceType
        from dganze_insurance_service_record m
        left join dgdoc_aqjd_cons_site n on m.project_id = n.id
        <where>
            m.audit_state = 0
            <if test="townShipId !=null">
                and n.township_id = #{townShipId}
            </if>
            <if test="id !=null">
                and m.id = #{id}
            </if>
            <if test="projectId != null and projectId.size > 0">
                AND m.project_id IN
                <foreach collection="projectId" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>
</mapper>